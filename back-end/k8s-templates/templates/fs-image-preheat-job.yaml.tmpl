{{- range .Jobs }}
---
apiVersion:  apps/v1
kind: Deployment
metadata:
  name: "{{.JobName}}"
  namespace: image-preheat
  labels:
    app: "{{.AppName}}"
spec:
  strategy:
    type: Recreate
  replicas: 1
  selector:
    matchLabels:
      app: "{{.AppName}}"
  template:
    metadata:
      labels:
        app: "{{.AppName}}"
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - "{{.AppName}}"
                topologyKey: kubernetes.io/hostname
              weight: 100
      initContainers:
        {{- range .InitContainers }}
        - name: "{{.Name}}"
          image: "{{.Image}}"
          imagePullPolicy: "Always"
          command: ["echo", "success"]
        {{- end }}
      containers:
        - name: "{{.AppName}}"
          image: reg.foneshare.cn/docker.io/alpine:latest
          command:
            - sleep
            - 240h
          resources:
            limits:
              cpu: 30m
              memory: 20Mi
            requests:
              cpu: 30m
              memory: 20Mi
{{- end }}