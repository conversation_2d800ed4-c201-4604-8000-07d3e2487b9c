package prom

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/push"
)

type I18nDiskUsage struct {
	App       string `json:"app"`
	Cluster   string `json:"cluster"`
	DirPath   string `json:"dirPath"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Size      int64  `json:"size"`
	Time      string `json:"time"`
}

func PushI18nDiskUsage(data []I18nDiskUsage) error {
	batchSize := 20
	total := len(data)
	if total == 0 {
		return nil
	}
	var ret error = nil
	for i := 0; i < total; i += batchSize {
		end := i + batchSize
		if end > total {
			end = total
		}
		batch := data[i:end]

		reg := prometheus.NewRegistry()

		dirSizeGauge := prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "fs_i18n_dir_size_usage_bytes",
				Help: "fs i18n dir size usage bytes",
			},
			[]string{"app", "cluster", "namespace", "pod", "dir_path"},
		)

		if err := reg.Register(dirSizeGauge); err != nil {
			return err
		}

		for _, item := range batch {
			dirSizeGauge.With(prometheus.Labels{
				"app":       item.App,
				"cluster":   item.Cluster,
				"namespace": item.Namespace,
				"pod":       item.Pod,
				"dir_path":  item.DirPath,
			}).Set(float64(item.Size))
		}

		if err := push.New("http://172.17.0.160:9091", "fs-k8s-app-manager-i18n-scan").
			Gatherer(reg).Push(); err != nil {
			ret = err
		}
	}
	return ret
}

func DeleteI18nDiskUsage() error {
	return push.New("http://172.17.0.160:9091", "fs-k8s-app-manager-i18n-scan").Delete()
}
