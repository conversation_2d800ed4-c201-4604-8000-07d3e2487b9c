package app

import (
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

const appNamesCacheKey = "AppNames"

type TreeNode struct {
	Value    string     `json:"value"`
	Label    string     `json:"label"`
	Children []TreeNode `json:"children"`
}

type SearchParams struct {
	param.PageSearch
	Level      string `form:"level"`
	Department string `form:"department"`
}

func List(c *gin.Context) {
	var p SearchParams
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "缺少参数! "+err.Error())
		return
	}

	data := make(map[string]interface{})
	if entities, err := app_service.Search(p.Keyword, p.Level, p.Department, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = dto.ToAppList(entities)
		data["count"] = app_service.Count(p.Keyword, p.Level, p.Department)
	}
	web.SuccessJson(c, data)
}

func SyncFromCMDB(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "无操作权限")
		return
	}
	ret, err := app_service.SyncFromCMDB()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.CreateBySys("应用-从CRM同步", "app", ret)
	web.SuccessJson(c, ret)
}
func AllNames(c *gin.Context) {
	if data, found := cache.GetStruct(key.Pre().APP.Key(appNamesCacheKey), []string{}); found {
		web.SuccessJson(c, data)
		return
	}
	data := make([]string, 0, 500)
	if entities, err := app_service.FindAll(); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		for _, it := range entities {
			data = append(data, it.Name)
		}
	}
	cache.SetStruct(key.Pre().APP.Key(appNamesCacheKey), data, 5*time.Minute)
	web.SuccessJson(c, data)
}

func FindByName(c *gin.Context) {
	app := c.Query("name")
	entity, err := app_service.FindByName(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, dto.ToApp(entity))
}
func DeleteByName(c *gin.Context) {
	app := c.Query("name")

	user, _ := auth.GetUser(c)
	if !perm_service.HasAppPerm(user, app) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "你没有应用权限")
		return
	}

	pipes, err := pipeline_service.FindByApp(app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(pipes) > 0 {
		web.FailJson(c, web.CODE_SERVER_ERROR, "应用下存在发布流程，不允许被删除")
		return
	}
	log_service.Create(auth.GetRealName(c), "应用-删除", app, "")
	if err := app_service.DeleteByName(app); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func Create(c *gin.Context) {
	var p dto.App
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	log_service.Create(auth.GetRealName(c), "应用-创建", p.Name, p)
	entity := p.ToModel()
	if err := app_service.Create(&entity); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	cache.Delete(key.Pre().APP.Key(appNamesCacheKey))
	web.SuccessJson(c, nil)
}

func Edit(c *gin.Context) {
	var p dto.App
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	dbEntity, err := app_service.FindByName(p.Name)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	isAdmin := perm_service.IsAdmin(user)
	if !isAdmin {
		//只有管理员才能修改发布窗口
		if dbEntity.TimeWindow.Desc() != "" && dbEntity.TimeWindow.Desc() != p.TimeWindow.Desc() {
			if !perm_service.IsTimeWindowAdmin(user) {
				web.FailJson(c, web.CODE_SERVER_ERROR, "只有【发版时间窗口-管理员】允许修改发版时间窗口")
				return
			}
		}
		//当应用已存在管理员时，则进行操作鉴权
		//if len(dbEntity.Admins) != 0 && !perm_service.IsAppAdmin(user, dbEntity.Name) {
		//	web.FailJson(c, web.CODE_SERVER_ERROR, "只有【应用管理员】允许执行当前操作")
		//	return
		//}
	}

	log_service.Create(auth.GetRealName(c), "应用-编辑", p.Name, p)
	dbEntity.Department = p.Department
	dbEntity.Orgs = p.Orgs
	dbEntity.Level = p.Level
	dbEntity.Remark = p.Remark
	//dbEntity.Admins = p.Admins
	dbEntity.TimeWindow = p.TimeWindow
	dbEntity.MainOwner = p.MainOwner
	dbEntity.Owners = p.Owners
	err = app_service.Update(&dbEntity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	app_service.ClearAppsCache()
	web.SuccessJson(c, nil)
}

func EditPermission(c *gin.Context) {
	var p PermissionParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAppOwner(user.RealName, p.App) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "只有应用的负责人才允许执行当前操")
		return
	}
	dbEntity, err := app_service.FindByName(p.App)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	log_service.Create(auth.GetRealName(c), "应用-权限编辑", p.App, p)
	dbEntity.Orgs = p.Orgs
	err = app_service.Update(&dbEntity)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	app_service.ClearAppsCache()
	web.SuccessJson(c, nil)
}

func Address(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	for _, it := range []string{cluster, namespace, app} {
		if it == "" {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "缺失查询参数")
			return
		}
	}

	service, err := k8s_service.ServiceDTO(cluster, namespace, app)
	if service == nil {
		err = fmt.Errorf("not found service from %s/%s/%s", cluster, namespace, app)
	}
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make([]dto.AppAddress, 0, len(service.Ports))
	clu := config.GetSetting().GetCluster(cluster)
	for _, it := range service.Ports {
		addrs := make([]string, 0, 2)
		if clu == nil {
			continue
		}

		lbAddr := config.GetLBPools().GetAppLBAddr(clu.Name, namespace, app)
		remark := ""
		if clu.ShowLBAddr {
			addrs = append(addrs, fmt.Sprintf("%s:%d", lbAddr, it.NodePort))
			remark = "地址在集群外、集群内均可访问服务"

			if pool, _ := config.GetLBPools().GetLBPool(clu.Name); pool.DefaultLBAddr != "" && pool.DefaultLBAddr != lbAddr {
				addr := fmt.Sprintf("%s:%d", pool.DefaultLBAddr, it.NodePort)
				addrs = append(addrs, addr)
				remark = fmt.Sprintf("%s<div style='color: orangered;'>%s 为旧地址，不建议使用</div>", remark, addr)
			}

		} else {
			addrs = append(addrs, fmt.Sprintf("%s.%s:%d", app, namespace, it.Port))
			remark = "地址仅在集群内可访问服务"
		}

		data = append(data, dto.AppAddress{
			Cluster:    cluster,
			Namespace:  namespace,
			App:        app,
			Name:       it.Name,
			Protocol:   it.Protocol,
			Port:       it.Port,
			TargetPort: it.TargetPort,
			NodePort:   it.NodePort,
			Addresses:  addrs,
			Remark:     remark,
		})
	}

	web.SuccessJson(c, data)
}

func CountPipelines(c *gin.Context) {
	apps, err := app_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	pipes, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	appPipes := make(map[string]int)
	for _, app := range apps {
		appPipes[app.Name] = 0
	}
	for _, pipe := range pipes {
		appPipes[pipe.App]++
	}
	web.SuccessJson(c, appPipes)
}

func AppsWithEnv(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("apps-with-env")
	if data, found := cache.GetStruct(cacheKey, map[string][]map[string]string{}); found {
		web.SuccessJson(c, data)
		return
	}
	pipes, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	data := make(map[string][]map[string]string)
	for _, p := range pipes {
		//if p.Status != constant.PIPELINE_STATUS_ENABLED {
		//	continue
		//}
		_, found := data[p.App]
		if !found {
			data[p.App] = make([]map[string]string, 0, 30)
		}
		envDesc := fmt.Sprintf("%s | %s", p.Namespace, p.Cluster)
		clusterDesc := ""
		clu := config.GetSetting().GetCluster(p.Cluster)
		if clu != nil && !clu.IsFxiaokeCloud() {
			envDesc = fmt.Sprintf("%s | %s", clu.Description, p.Cluster)
			//如果不是专属云下的主环境，则带上数字后缀
			match := regexp.MustCompile(`-(\d+)$`).FindStringSubmatch(p.Namespace)
			if len(match) > 1 {
				envDesc = fmt.Sprintf("%s-%s | %s", clu.Description, match[1], p.Cluster)
			}
		}
		if clu != nil {
			clusterDesc = clu.Description
		}
		data[p.App] = append(data[p.App], map[string]string{
			"cluster":        p.Cluster,
			"namespace":      p.Namespace,
			"clusterDesc":    clusterDesc,
			"envDesc":        envDesc,
			"replicas":       strconv.Itoa(int(p.Replicas)),
			"pipelineStatus": p.StatusDesc(),
		})
	}
	for app, _ := range data {
		if len(data[app]) > 1 {
			sort.Slice(data[app], func(i, j int) bool {
				itemI := config.GetSetting().GetEnvSortValue(data[app][i]["cluster"], data[app][i]["namespace"])
				itemJ := config.GetSetting().GetEnvSortValue(data[app][j]["cluster"], data[app][j]["namespace"])
				return itemI < itemJ
			})
		}
	}

	_ = cache.SetStruct(cacheKey, data, 8*time.Hour)
	web.SuccessJson(c, data)
}

func GroupByNamespace(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("group-by-namespace")
	if data, found := cache.GetStruct(cacheKey, []TreeNode{}); found {
		web.SuccessJson(c, data)
		return
	}
	pipes, err := pipeline_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	treeNodes := make(map[string]map[string][]TreeNode)
	for _, p := range pipes {
		_, found := treeNodes[p.Cluster]
		if !found {
			treeNodes[p.Cluster] = make(map[string][]TreeNode)
		}
		_, found = treeNodes[p.Cluster][p.Namespace]
		if !found {
			treeNodes[p.Cluster][p.Namespace] = make([]TreeNode, 0, 200)
		}
		treeNodes[p.Cluster][p.Namespace] = append(treeNodes[p.Cluster][p.Namespace], TreeNode{
			Value:    p.App,
			Label:    p.App,
			Children: nil,
		})
	}

	data := make([]TreeNode, 0, len(treeNodes))
	for cluster, namespaces := range treeNodes {
		clusterName := cluster
		clu := config.GetSetting().GetCluster(cluster)
		if clu != nil && clu.Description != "" {
			clusterName = fmt.Sprintf("%s(%s)", clu.Description, clu.Name)
		}
		clusterNode := TreeNode{
			Value:    cluster,
			Label:    clusterName,
			Children: make([]TreeNode, 0, len(namespaces)),
		}
		for namespace, apps := range namespaces {
			namespaceNode := TreeNode{
				Value:    namespace,
				Label:    namespace,
				Children: apps,
			}
			clusterNode.Children = append(clusterNode.Children, namespaceNode)
		}
		data = append(data, clusterNode)
	}
	sort.Slice(data, func(i, j int) bool {
		weightI := 100
		weightJ := 100
		if idx := config.GetSetting().ClusterIndexOf(data[i].Value); idx >= 0 {
			weightI = idx
		}
		if idx := config.GetSetting().ClusterIndexOf(data[j].Value); idx >= 0 {
			weightJ = idx
		}
		return weightI < weightJ
	})
	_ = cache.SetStruct(cacheKey, data, 60*time.Minute)
	web.SuccessJson(c, data)
}

func All(c *gin.Context) {
	cacheKey := key.Pre().APP.Key("all-apps")
	if data, found := cache.GetStruct(cacheKey, []dto.App{}); found {
		web.SuccessJson(c, data)
		return
	}
	entities, err := app_service.FindAll()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	data := dto.ToAppList(entities)
	_ = cache.SetStruct(cacheKey, data, 8*time.Hour)
	web.SuccessJson(c, data)
}

func GitModules(c *gin.Context) {
	app := c.Query("app")
	pipelineId := c.Query("pipelineId")

	pipes := make([]models.Pipeline, 0, 1)
	if app != "" {
		appObj, err := app_service.FindByName(app)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		pipes, err = pipeline_service.FindByApp(appObj.Name)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	} else if pipelineId != "" {
		pipeIdUint, err := strconv.ParseUint(pipelineId, 10, 32)
		if err != nil {
			web.FailJson(c, web.CODE_CLIENT_ERROR, "pipelineId参数格式错误")
			return
		}
		pipe, err := pipeline_service.FindById(uint(pipeIdUint))
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		pipes = append(pipes, pipe)
	}
	data := make([]map[string]string, 0, len(pipes))
	for _, pipe := range pipes {
		mavenImage, err := getMavenImage(pipe.BaseImage)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, "获取Maven镜像失败，"+err.Error())
			return
		}
	ModuleLoop:
		for _, mod := range pipe.AppModules {
			for _, d := range data {
				if d["gitUrl"] == mod.GitUrl && d["module"] == mod.Module {
					continue ModuleLoop
				}
			}
			//mavenImage: 当多个发布流程使用了不同jdk时， 需要考虑使用哪个maven镜像更合适
			data = append(data, map[string]string{
				"gitUrl":     mod.GitUrl,
				"module":     mod.Module,
				"mavenImage": mavenImage,
			})
		}
	}
	sort.Slice(data, func(i, j int) bool {
		if data[i]["gitUrl"] < data[j]["gitUrl"] {
			return true
		} else if data[i]["gitUrl"] > data[j]["gitUrl"] {
			return false
		} else {
			return data[i]["module"] < data[j]["module"]
		}
	})
	web.SuccessJson(c, data)
}

func getMavenImage(image string) (string, error) {
	jdk, err := extractJDK(image)
	if err != nil {
		return "", err
	}

	// 保持原逻辑：直接使用提取的JDK版本
	targetJdk := jdk
	// 检查是否启用Maven镜像优化选择逻辑
	if config.GetSetting().EnableMavenImageOptimization {
		// 根据新的规则选择Maven镜像
		targetJdk = selectMavenJdk(jdk)
	}

	for _, it := range config.GetSetting().MavenImages {
		if strings.HasSuffix(it, targetJdk) {
			return it, nil
		}
	}
	return "", fmt.Errorf("未找到匹配的Maven版本: %s", targetJdk)
}

func selectMavenJdk(jdk string) string {
	// 1. 如果是dragonwell镜像，继续使用dragonwell
	if strings.HasPrefix(jdk, "dragonwell") {
		return jdk
	}

	// 2. 如果是Java 21以上的，继续使用Java 21以上版本，比如Java 25
	if strings.HasPrefix(jdk, "openjdk2") {
		return jdk
	}

	// 3. 其他都默认使用Java 21
	return "openjdk21"
}

func extractJDK(image string) (string, error) {
	//查找openjdk
	re := regexp.MustCompile(`openjdk(\d+)`)
	matches := re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "openjdk" + matches[1], nil
	}
	//查找dragonwell jdk
	re = regexp.MustCompile(`dragonwell(\d+)`)
	matches = re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "dragonwell" + matches[1], nil
	}
	return "", fmt.Errorf("未找到匹配的JDK版本")
}
