package imagepreheat

import (
	"fmt"
	"fs-k8s-app-manager/config"
	k8s_templates "fs-k8s-app-manager/k8s-templates"
	"fs-k8s-app-manager/models"
	k8s_client "fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/middleware/auth"
	k8s_util "fs-k8s-app-manager/pkg/util/k8s"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/executor/task_executor"
	"fs-k8s-app-manager/service/image_preheat_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/task_service"
	"fs-k8s-app-manager/web"
	"github.com/prometheus/common/log"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"

	"github.com/gin-gonic/gin"
)

const jobName = "fs-image-preheat-job"

type CreateParam struct {
	Name             string   `form:"name" binding:"required"`      // 名称
	ImageInEnvs      []string `form:"imageInEnvs" binding:"-"`      // 镜像所在的环境
	SpecifiedImages  []string `form:"images" binding:"-"`           // 指定的镜像
	ImageRecentHours int32    `form:"imageRecentHours" binding:"-"` // 最近多少小时内构建的镜像
}

type ExecuteParam struct {
	ImagePreheatId uint     `form:"imagePreheatId" binding:"required"`
	Clusters       []string `form:"clusters" binding:"-"`
	JobNumber      int      `form:"jobNumber" binding:"required"`
}

type DeleteJobsParam struct {
	Cluster   string   `form:"cluster" binding:"required"`
	Namespace string   `form:"namespace" binding:"required"`
	Names     []string `form:"names" binding:"required"`
}

func Create(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "只有管理员才有权限操作")
		return
	}
	var param CreateParam
	if err := c.ShouldBindJSON(&param); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	model := models.ImagePreheat{}
	queryMap := map[string]interface{}{
		"envs":        param.ImageInEnvs,
		"images":      param.SpecifiedImages,
		"recentHours": param.ImageRecentHours,
	}

	imageList := make([]string, 0, 1000)

	for _, image := range param.SpecifiedImages {
		if !strslice.Find(imageList, image) {
			imageList = append(imageList, image)
		}
	}

	tasks, err := task_service.SearchAfterTime(string(models.TASK_TYPE_BUILD),
		[]string{string(models.TASK_PHASE_SUCCESS)},
		time.Now().Add(-time.Duration(param.ImageRecentHours)*time.Hour))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range tasks {
		var taskParams task_executor.BuildParam
		if err := mapstructure.Decode(it.Params, &taskParams); err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		if !strslice.Find(imageList, taskParams.ArtifactImage) {
			imageList = append(imageList, taskParams.ArtifactImage)
		}
	}

	for _, env := range param.ImageInEnvs {
		envs := strings.Split(env, "/")
		cluster := envs[0]
		namespace := envs[1]
		images, err := getInitImagesFronEnv(cluster, namespace)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for _, image := range images {
			if !strslice.Find(imageList, image) {
				imageList = append(imageList, image)
			}
		}
	}

	sort.Strings(imageList)
	model.Name = param.Name
	model.Query = queryMap
	model.Images = imageList
	model.Creator = user.RealName
	err = image_preheat_service.Save(&model)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, model)
}

func Search(c *gin.Context) {
	keyword := c.Query("keyword")
	ret, err := image_preheat_service.Search(keyword)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, ret)
}

func getInitImagesFronEnv(cluster, namespace string) ([]string, error) {
	ret := make([]string, 0, 1000)
	deps, err := k8s_client.GetDeploymentList(cluster, namespace)
	if err == nil {
		for _, dep := range deps.Items {
			if *dep.Spec.Replicas < 1 {
				continue
			}
			for _, c := range dep.Spec.Template.Spec.InitContainers {
				if !strslice.Find(ret, c.Image) {
					ret = append(ret, c.Image)
				}
			}
		}
	}
	return ret, err
}

func Delete(c *gin.Context) {
	id := c.Query("id")
	if id == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "id is required")
		return
	}
	// Convert id from string to uint
	uid, err := strconv.ParseUint(id, 10, 64)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "invalid id")
		return
	}
	err = image_preheat_service.Delete(uint(uid))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func DownloadJobYaml(c *gin.Context) {
	var param ExecuteParam
	if err := c.ShouldBindQuery(&param); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	entity, err := image_preheat_service.FindByID(param.ImagePreheatId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	jobContent, err := buildJobYaml(entity.Images, param.JobNumber)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	fileName := fmt.Sprintf("%s-%d-%s.yaml", jobName, len(entity.Images), time.Now().Format("20060102"))
	c.Header("Content-Disposition", "attachment; filename="+fileName)
	c.Header("Content-Type", "application/x-yaml")
	c.String(200, jobContent)

}

func PreheatWithCurrClusterImage(c *gin.Context) {
	cluster := c.Query("cluster")
	jobNumber := c.Query("jobNumber")
	jobNumberInt, err := strconv.Atoi(jobNumber)
	if err != nil {
		jobNumberInt = 6
	}

	clu := config.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("集群%s不存在", cluster))
		return
	}
	imageList := make([]string, 0, 1000)
	for _, ns := range clu.Namespaces {
		images, err := getInitImagesFronEnv(cluster, ns)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for _, image := range images {
			if !strslice.Find(imageList, image) {
				imageList = append(imageList, image)
			}
		}
	}

	if err := createJobsInCluster(*clu, imageList, jobNumberInt); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)

}
func ExecuteJobs(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "只有管理员才有权限操作")
		return
	}
	resp := make([]string, 0, 10)
	var param ExecuteParam
	if err := c.ShouldBindJSON(&param); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if len(param.Clusters) < 1 {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "请选择需要预热的集群")
		return
	}
	entity, err := image_preheat_service.FindByID(param.ImagePreheatId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	for _, cluster := range param.Clusters {
		clu := config.GetSetting().GetCluster(cluster)
		if clu == nil {
			resp = append(resp, fmt.Sprintf("集群%s：创建失败，err：集群不存在", cluster))
			continue
		}
		if err := createJobsInCluster(*clu, entity.Images, param.JobNumber); err != nil {
			resp = append(resp, fmt.Sprintf("集群%s：创建失败，err：%v", cluster, err))
		} else {
			resp = append(resp, fmt.Sprintf("集群%s：创建成功", cluster))
		}
	}
	web.SuccessJson(c, strings.Join(resp, " | "))
}

func createJobsInCluster(clu config.Cluster, images []string, jobNumber int) error {
	images = strslice.Map(images, func(s string) string {
		return clu.NormalImage2ProxyImage(s)
	})
	jobContent, _ := buildJobYaml(images, jobNumber)
	if err := kubectl.ApplyOrReplace(clu.Name, "image-preheat", jobContent, "apply"); err != nil {
		return err
	}
	return nil
}

func buildJobYaml(images []string, jobNumber int) (string, error) {
	jobs := make([]map[string]any, 0, 10)
	imageGroup := make([][]string, 0, jobNumber)
	for i := 0; i < jobNumber; i++ {
		imageGroup = append(imageGroup, []string{})
	}
	for i, image := range images {
		imageGroup[i%jobNumber] = append(imageGroup[i%jobNumber], image)
	}
	timestamp := time.Now().Format("20060102")
	for i, images := range imageGroup {
		if len(images) < 1 {
			continue
		}
		initContainers := make([]map[string]any, 0, len(images))
		for j, image := range images {
			initContainers = append(initContainers, map[string]any{
				"Name":  fmt.Sprintf("image-%d-%d", i+1, j+1),
				"Image": image,
			})
		}
		jobs = append(jobs, map[string]any{
			"JobName":        fmt.Sprintf("%s-%s-%d", jobName, timestamp, i+1),
			"AppName":        jobName,
			"InitContainers": initContainers,
		})
	}
	params := map[string]any{
		"Jobs": jobs,
	}
	yaml, err := k8s_templates.BuildContentWithFilename("fs-image-preheat-job.yaml.tmpl", params)
	if err != nil {
		return "", err
	}
	return yaml, nil
}

func GetImagePreheatJobs(c *gin.Context) {
	cluster := c.Query("cluster")
	clu := config.GetSetting().GetCluster(cluster)
	if clu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("集群%s不存在", cluster))
		return
	}
	namespace := c.DefaultQuery("namespace", "image-preheat")
	lines, err := kubectl.ListPodInEnv(cluster, namespace)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	pods := make([]map[string]any, 0, len(lines))
	sort.Strings(lines)
	if len(lines) > 0 {
		for _, line := range lines {
			items := strings.Fields(line)
			if len(items) < 5 {
				continue
			}

			pod := map[string]any{
				"name":     items[0],
				"jobName":  k8s_util.GetAppName(items[0]),
				"ready":    items[1],
				"status":   items[2],
				"restarts": items[3],
				"age":      items[4],
			}
			pods = append(pods, pod)
		}
	}
	web.SuccessJson(c, pods)
}

func DeleteImagePreheatJobs(c *gin.Context) {
	var param DeleteJobsParam
	if err := c.ShouldBindJSON(&param); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	for _, name := range param.Names {
		if err := kubectl.DeleteDeployment(param.Cluster, param.Namespace, name); err != nil {
			log.Warnf("delete deployment %s in %s/%s error: %v", name, param.Cluster, param.Namespace, err)
		}
	}
	web.SuccessJson(c, nil)
}
