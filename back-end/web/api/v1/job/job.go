package job

import (
	"errors"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/client/gitlab"
	"fs-k8s-app-manager/pkg/client/harbor"
	"fs-k8s-app-manager/pkg/constant"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/docker"
	"fs-k8s-app-manager/pkg/util/times"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/artifact_image_service"
	"fs-k8s-app-manager/service/executor/job_executor"
	"fs-k8s-app-manager/service/executor/task_executor"
	"fs-k8s-app-manager/service/job_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/parent_pom_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/task_service"
	"fs-k8s-app-manager/service/temp_auth_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/api/v1/tool/harbor_tool"
	"fs-k8s-app-manager/web/param"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

type BuildImageParam struct {
	App              string `json:"app" form:"app" binding:"required"`
	GitUrl           string `json:"gitUrl" form:"gitUrl" binding:"required"`
	GitModule        string `json:"gitModule" form:"gitModule"`
	GitTag           string `json:"gitTag" form:"gitTag" binding:"required"`
	CommitId         string `json:"commitId" form:"commitId"`
	MavenImage       string `json:"mavenImage" form:"mavenImage" binding:"required"`
	UnitTest         bool   `json:"unitTest" form:"unitTest"`
	ForceCodeCompile bool   `json:"forceCodeCompile" form:"forceCodeCompile"`
	DependencyCheck  bool   `json:"dependencyCheck" form:"dependencyCheck"`
	ParentPom        string `json:"parentPom" form:"parentPom" binding:"required"`
	Remark           string `json:"remark" form:"remark"`
	ArtifactImage    string `json:"artifactImage" form:"artifactImage"`
}

type BuildImageResult001 struct {
	BuildImageParam
	JobId       uint `json:"jobId"`
	BeforeJobId uint `json:"beforeJobId"`
}

type BuildImageParamList struct {
	Items []BuildImageParam `json:"items" form:"items" binding:"dive"`
}

type SearchParam struct {
	param.PageSearch
	Type   string            `form:"type" binding:"required"`
	App    string            `form:"app"`
	Author string            `form:"author"`
	Status []string          `form:"status"`
	Params map[string]string `form:"params"`
}

type BuildAndDeployParam struct {
	BuildImageParam []BuildImageParam `json:"buildImageParam" form:"buildImageParams" binding:"dive"`
	DeployParam     []DeployParam     `json:"deployParam" form:"deployParam" binding:"dive"`
}

type DeployModuleImage struct {
	GitUrl    string `json:"gitUrl" form:"gitUrl" binding:"required"`
	GitModule string `json:"gitModule" form:"gitModule"`
	Image     string `json:"image" form:"image" binding:"required"`
}

type DeployParam struct {
	PipelineId         uint                `json:"pipelineId" form:"pipelineId" binding:"required"`
	MaxSurge           string              `json:"maxSurge" form:"maxSurge" binding:"required"`
	Remark             string              `json:"remark" form:"remark"`
	EolinkerTest       bool                `json:"eolinkerTest" form:"eolinkerTest"`
	DeployModuleImages []DeployModuleImage `json:"deployModuleImages" form:"deployModuleImages" binding:"dive"`
}

type DeployParamList struct {
	Items []DeployParam `json:"items" form:"items" binding:"dive"`
}

type DeployResult001 struct {
	DeployParam
	JobId       uint            `json:"jobId"`
	BeforeJobId uint            `json:"beforeJobId"`
	Pipeline    models.Pipeline `json:"-"`
}

type JobId struct {
	JobId uint `form:"jobId" binding:"min=1"`
}

func FindById(c *gin.Context) {
	var p param.Id
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	entity, err := job_service.FindById(p.Id)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, entity)
}

func FindTasks(c *gin.Context) {
	var p JobId
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	entities, err := task_service.FindByJob(p.JobId)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, entities)
}

func Cancel(c *gin.Context) {
	var p param.Id
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	//todo: 鉴权
	job, err := job_service.FindById(p.Id)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	tasks, err := task_service.FindByJob(job.ID)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, task := range tasks {
		if task.IsEnd() {
			continue
		}
		err = task_service.UpdateStatus(task.ID, models.TASK_PHASE_CANCEL)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	}
	err = job_service.UpdateStatus(job.ID, models.JOB_PHASE_CANCEL)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}

func ModifyJobStatus(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "无权限操作")
		return
	}

	idStr := c.Query("id")
	status := c.Query("status")
	if idStr == "" || status == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "参数缺失")
		return
	}
	id, err := strconv.Atoi(idStr)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	job, err := job_service.FindById(uint(id))
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	tasks, err := task_service.FindByJob(job.ID)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	var taskPhase models.TaskPhase
	var jobPhase models.JobPhase

	if status == "success" {
		taskPhase = models.TASK_PHASE_SUCCESS
		jobPhase = models.JOB_PHASE_SUCCESS
	} else if status == "fail" {
		taskPhase = models.TASK_PHASE_FAIL
		jobPhase = models.JOB_PHASE_FAIL
	} else {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "状态错误")
		return
	}

	for _, task := range tasks {
		if task.IsEnd() {
			continue
		}
		_ = task_service.UpdateStatus(task.ID, taskPhase)
	}
	_ = job_service.UpdateStatus(job.ID, jobPhase)
	if job.EndTime.Time().IsZero() {
		_ = job_service.UpdateEndTime(job.ID, time.Now())
	}
	log_service.Create(user.RealName, "任务-状态修改", job.App, map[string]interface{}{
		"id":     id,
		"status": status,
	})
	web.SuccessJson(c, nil)
}

func Redo(c *gin.Context) {
	user, _ := auth.GetUser(c)
	var p param.Id
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	//todo: 鉴权
	job, err := job_service.FindById(p.Id)
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	if !perm_service.IsAdmin(user) {
		if job.Type == models.JOB_TYPE_CI {
			if config.GetSetting().Maintain.CI.Open {
				web.FailJson(c, web.CODE_CLIENT_ERROR, config.GetSetting().Maintain.CI.Desc)
				return
			}
		} else if job.Type == models.JOB_TYPE_CD {
			if config.GetSetting().Maintain.CD.Open {
				web.FailJson(c, web.CODE_CLIENT_ERROR, config.GetSetting().Maintain.CD.Desc)
				return
			}
		} else {
			web.FailJson(c, web.CODE_CLIENT_ERROR, fmt.Sprintf("不支持的操作类型，job type: %s", job.Type))
			return
		}
	}

	tasks, err := task_service.FindByJob(job.ID)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	job2 := models.Job{
		BaseModel:   models.BaseModel{},
		Status:      models.JOB_PHASE_WAIT,
		BeforeJobId: 0,
		Type:        job.Type,
		App:         job.App,
		PipelineId:  job.PipelineId,
		Params:      job.Params,
		Remark:      job.Remark,
		Author:      user.RealName,
		StartTime:   models.DBTime{},
		EndTime:     models.DBTime{},
	}
	job2Tasks := make([]models.Task, 0, len(tasks))
	for _, task := range tasks {
		t := models.Task{
			BaseModel:      models.BaseModel{},
			JobID:          0,
			Status:         models.TASK_PHASE_WAIT,
			Type:           task.Type,
			Params:         task.Params,
			Attributes:     nil,
			Title:          task.Title,
			Summary:        task.Summary,
			Remark:         task.Remark,
			Output:         "",
			Author:         user.RealName,
			TimeoutSeconds: task.TimeoutSeconds,
		}
		job2Tasks = append(job2Tasks, t)
	}
	if err := job_service.Create(&job2); err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, task := range job2Tasks {
		task.JobID = job2.ID
		if err := task_service.Create(&task); err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
	}
	job_executor.SubmitJob(job2)
	web.SuccessJson(c, job2)
}

func Search(c *gin.Context) {
	var p SearchParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	data := make(map[string]interface{})

	if count, entities, err := job_service.Search(p.Type, p.Status, p.App, p.Author, p.Params, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = entities
		data["count"] = count
	}
	web.SuccessJson(c, data)
}

func ImageOptions(c *gin.Context) {
	ids := c.Query("pipelineIds")
	if ids == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "请选择发布流程")
		return
	}
	pipeIds := make([]uint, 0, 20)
	for _, item := range strings.Split(ids, ",") {
		if pipeId, err := strconv.Atoi(item); err != nil {
			web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
			return
		} else {
			pipeIds = append(pipeIds, uint(pipeId))
		}
	}
	pipelines := make([]models.Pipeline, 0, len(pipeIds))
	//todo: 校验
	for _, item := range pipeIds {
		if pipe, err := pipeline_service.FindById(item); err != nil {
			web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
			return
		} else {
			pipelines = append(pipelines, pipe)
		}
	}

	if len(pipelines) > 1 {
		for _, pipe := range pipelines {
			if pipe.Cluster == "forceecrm-k8s1" {
				web.FailJson(c, web.CODE_CLIENT_ERROR, "批量发布不允许包含 模版复制云 ")
				return
			}
		}
	}

	benchmarkPipe := pipelines[0]
	benchmarkPipeClu := config.GetSetting().GetCluster(benchmarkPipe.Cluster)
	if benchmarkPipeClu == nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "集群不存在, name: "+benchmarkPipe.Cluster)
	}
	var currDepModules datatype.DeployModules
	if dep, er := k8s_service.DeploymentDetail(benchmarkPipe.Cluster, benchmarkPipe.Namespace, benchmarkPipe.App); er == nil {
		currDepModules, _ = k8s_service.GetDeployModules(dep)
	}

	var data []map[string]interface{}
	wg := sync.WaitGroup{}
	wg.Add(len(benchmarkPipe.AppModules))

	poms := make([]string, 0, 5)
	if benchmarkPipe.Cluster == "forceecrm-k8s1" {
		poms = append(poms, "fxiaoke-parent-pom-forceecrm-rc")
	} else {
		parentPoms, err := parent_pom_service.FindAll()
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		for _, p := range parentPoms {
			if p.IsAllowNamespace(benchmarkPipe.Namespace) {
				poms = append(poms, p.Name)
			}
		}
	}

	for _, m := range benchmarkPipe.AppModules {
		go func(m datatype.AppModule) {
			defer wg.Done()
			var images []map[string]string
			for _, p := range poms {
				repo := harbor_tool.BuildRepository(m.GitUrl, m.Module, p)
				artis, _, err := harbor.Create().GetArtifacts(config.GetConfig().Harbor.ArtifactProject, repo, 1, 100)
				if err != nil {
					web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
					return
				}
				for _, it := range artis {
					for _, tag := range it.Tags {
						createTime := tag.PushTime
						if t, err := times.ConvertUTCToChinaTime(tag.PushTime); err == nil {
							createTime = t.Format("06-01-02 15:04")
						}

						parentPomShortName := strings.ReplaceAll(p, "fxiaoke-parent-pom-", "")
						if p == "fxiaoke-parent-pom" {
							parentPomShortName = "开发版"
						}
						if strings.Contains(p, "fxiaoke-parent-pom-") {
							parentPomShortName = p[len("fxiaoke-parent-pom-"):]
						}
						images = append(images, map[string]string{
							"name":               harbor_tool.BuildArtifactFullName(m.GitUrl, m.Module, p, tag.Name),
							"tag":                tag.Name,
							"gitTag":             it.ExtraAttrs.Config.GitTag(),
							"parentPom":          p,
							"parentPomShortName": parentPomShortName,
							"createTime":         createTime,
							"remark":             it.ExtraAttrs.Config.BuildRemark(),
						})
					}
				}
			}
			sort.Slice(images, func(i, j int) bool {
				return images[i]["createTime"] > images[j]["createTime"]
			})

			imageSelected := ""
			if len(currDepModules) > 0 {
			Loop:
				for _, mod := range currDepModules {
					modUrl1 := strings.ReplaceAll(strings.ReplaceAll(mod.GitUrl, "https://", ""), "http://", "")
					modUrl2 := strings.ReplaceAll(strings.ReplaceAll(m.GitUrl, "https://", ""), "http://", "")
					if modUrl1 == modUrl2 && mod.Module == m.Module {
						for _, image := range images {
							//把代理镜像转换为原始镜像
							artifactImage := benchmarkPipeClu.ProxyImage2NormalImage(mod.ArtifactImage)
							if image["name"] == artifactImage {
								imageSelected = artifactImage
								break Loop
							}
						}
					}
				}
			}
			data = append(data, map[string]interface{}{
				"gitUrl":        m.GitUrl,
				"gitModule":     m.Module,
				"imageSelected": imageSelected,
				"images":        images,
			})
		}(m)
	}
	wg.Wait()
	sort.Slice(data, func(i, j int) bool {
		item1 := data[i]
		item2 := data[j]
		item1Val := fmt.Sprintf("%s#%s", item1["gitUrl"], item1["gitModule"])
		item2Val := fmt.Sprintf("%s#%s", item2["gitUrl"], item2["gitModule"])
		return item1Val < item2Val
	})
	web.SuccessJson(c, data)
}

func allowBuildImage(app string, user models.User) (bool, string) {
	//管理员判断
	if perm_service.IsAdmin(user) {
		return true, ""
	}
	//是否有临时授权，有临时授权的话，可以在维护期进行操作（场景：紧急发版)
	tempAuth, _ := temp_auth_service.FindByAppAndUserAndOperate(app, user.RealName, constant.TEMP_AUTH_OPERATE_DEPLOY)
	hasTempAuth := tempAuth.ID != 0 && tempAuth.Approved && tempAuth.NowInTimeRange()
	if hasTempAuth {
		return true, ""
	}
	//系统是否处于关闭/维护状态
	if config.GetSetting().Maintain.CI.Open {
		return false, config.GetSetting().Maintain.CI.Desc
	}
	//是否有应用发版权限
	if !perm_service.HasAppPerm(user, app) {
		return false, fmt.Sprintf("您没有[%s]应用的发版权限", app)
	}
	return true, ""
}

func BuildImage(c *gin.Context) {
	var p BuildImageParamList
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	buildRet, err := BuildImageWithParam(c, p, true)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range buildRet {
		job, err := job_service.FindById(it.JobId)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//todo: 如果任务失败，考虑回收已提交任务
		job_executor.SubmitJob(job)
	}
	web.SuccessJson(c, buildRet)
}

// BuildImageWithParam 构建镜像
// c 上下文
// p 构建镜像参数
// parallelExecute 是否并行执行
func BuildImageWithParam(c *gin.Context, p BuildImageParamList, parallelExecute bool) (ret []BuildImageResult001, er error) {
	if len(p.Items) == 0 {
		return nil, errors.New("请选择构建镜像的部署模块")
	}
	ret = make([]BuildImageResult001, 0, len(p.Items))

	user, _ := auth.GetUser(c)
	for _, it := range p.Items {
		if allow, msg := allowBuildImage(it.App, user); !allow {
			return nil, errors.New(msg)
		}
	}

	var beforeId = uint(0)
	for _, it := range p.Items {
		if last, err := job_service.LastByType(models.JOB_TYPE_CI, map[string]string{
			"gitUrl":    it.GitUrl,
			"gitModule": it.GitModule,
		}); err == nil {
			if !last.IsEnd() {
				return nil, fmt.Errorf("模块(%s/%s)已有一个构建任务(id: %d)在运行中，请等待任务结束后再尝试。", it.GitUrl, it.GitModule, last.ID)
			}
		}
		if len(it.CommitId) == 0 {
			commit, err := gitlab.GetCommit(it.GitUrl, it.GitTag)
			if err != nil || commit == nil {
				return nil, fmt.Errorf("get commit id failed, gitUrl: %s, tag: %s, err: %v", it.GitUrl, it.GitTag, err)
			}
			it.CommitId = commit.ID
		}

		artifactImage := harbor_tool.BuildArtifactFullName(it.GitUrl, it.GitModule, it.ParentPom, docker.GitTag2DockerTag(it.GitTag))
		if tagExist, err := gitlab.TagIsExist(it.GitUrl, it.GitTag); err != nil {
			return nil, err
		} else if !tagExist {
			//如果为分支，则镜像名带上时间后缀
			artifactImage = harbor_tool.BuildArtifactFullName(it.GitUrl, it.GitModule, it.ParentPom,
				docker.GitTag2DockerTag(fmt.Sprintf("%s--%s", it.GitTag, time.Now().Format("200601021504"))))
		}
		it.ArtifactImage = artifactImage

		buildParam := createBuildTaskParam(it)
		if !it.ForceCodeCompile {
			exists, err := imageExists(buildParam.ArtifactImage)
			if err != nil {
				return nil, err
			}
			if exists {
				return nil, errors.New("同名镜像已存在。如果需要继续，请勾选【如果镜像存在，则覆盖】")
			}
		}
		buildParamMap := make(map[string]interface{})
		if err := mapstructure.Decode(buildParam, &buildParamMap); err != nil {
			return nil, err
		}

		it.Remark = strings.ReplaceAll(it.Remark, "\n", `\n`)
		jobParam := map[string]interface{}{
			"gitUrl":           it.GitUrl,
			"gitModule":        it.GitModule,
			"gitTag":           it.GitTag,
			"commitId":         it.CommitId,
			"artifactImage":    it.ArtifactImage,
			"mavenImage":       it.MavenImage,
			"unitTest":         it.UnitTest,
			"forceCodeCompile": it.ForceCodeCompile,
			"dependencyCheck":  it.DependencyCheck,
			"parentPom":        it.ParentPom,
			"remark":           it.Remark,
		}
		job := models.Job{
			Status:      models.JOB_PHASE_WAIT,
			Type:        models.JOB_TYPE_CI,
			App:         it.App,
			Author:      user.RealName,
			Remark:      it.Remark,
			Params:      jobParam,
			BeforeJobId: beforeId,
		}

		if err := job_service.Create(&job); err != nil {
			return nil, err
		}
		task := models.Task{
			JobID:          job.ID,
			Status:         models.TASK_PHASE_WAIT,
			Type:           models.TASK_TYPE_BUILD,
			Params:         buildParamMap,
			Attributes:     nil,
			Title:          "镜像构建",
			Summary:        "",
			Output:         "",
			Remark:         it.Remark,
			Author:         user.RealName,
			TimeoutSeconds: 3600,
		}
		if err := task_service.Create(&task); err != nil {
			return nil, err
		}
		if err := artifact_image_service.Create(&models.ArtifactImage{
			Image:       it.ArtifactImage,
			GitUrl:      it.GitUrl,
			GitModule:   it.GitModule,
			GitRef:      it.GitTag,
			GitCommitId: it.CommitId,
			Author:      user.RealName,
			App:         it.App,
		}); err != nil {
			return nil, err
		}

		ret = append(ret, BuildImageResult001{
			BuildImageParam: it,
			JobId:           job.ID,
			BeforeJobId:     job.BeforeJobId,
		})
		if !parallelExecute {
			beforeId = job.ID
		}
	}
	return ret, nil
}

func imageExists(imageFullName string) (bool, error) {
	_, project, repo, tag, err := harbor_tool.ParseMetaFromArtifactFullName(imageFullName)
	if err != nil {
		return false, err
	}
	return harbor.Create().ArtifactExist(project, repo, tag)
}

func createBuildTaskParam(p BuildImageParam) task_executor.BuildParam {
	buildParam := task_executor.BuildParam{
		JenkinsJob:        "fs-k8s-app-manager--ci",
		MavenImage:        p.MavenImage,
		UnitTest:          p.UnitTest,
		ForceCodeCompile:  p.ForceCodeCompile,
		DependencyCheck:   p.DependencyCheck,
		ParentPom:         p.ParentPom,
		GitUrl:            p.GitUrl,
		GitModule:         p.GitModule,
		GitTag:            p.GitTag,
		CommitId:          p.CommitId,
		ArtifactBaseImage: config.GetConfig().Harbor.ArtifactBaseImage,
		ArtifactImage:     p.ArtifactImage,
	}
	return buildParam
}

func allowRunDeployJob(pipe models.Pipeline, user models.User) (bool, string) {
	//私有云环境只允许固定人员（管理员也不能例外）发布，必须先判断
	app, err := app_service.FindByName(pipe.App)
	if err != nil {
		return false, fmt.Sprintf("应用 %s 不存在, err: %v", pipe.App, err)
	}
	clu := config.GetSetting().GetCluster(pipe.Cluster)
	if clu != nil && clu.OpsDeploy {
		if !perm_service.IsOpsDeployAdmin(user) {
			return false, fmt.Sprintf("%s 环境只允许运维同学发布，可发布的人员：%s", clu.Name, strings.Join(perm_service.ListOpsDeployAdmin(), ", "))
		}
	}

	//管理员判断
	if perm_service.IsAdmin(user) {
		return true, ""
	}
	//是否有临时授权，有临时授权的话，可以在维护期进行操作（场景：紧急发版)
	tempAuth, _ := temp_auth_service.FindByAppAndUserAndOperate(app.Name, user.RealName, constant.TEMP_AUTH_OPERATE_DEPLOY)
	hasTempAuth := tempAuth.ID != 0 && tempAuth.Approved && tempAuth.NowInTimeRange()
	if hasTempAuth {
		return true, ""
	}
	//系统是否处于关闭/维护状态
	if config.GetSetting().Maintain.CD.Open {
		if !config.GetSetting().TimeWindow.IsSkip(pipe.Namespace) {
			return false, config.GetSetting().Maintain.CD.Desc
		}
	}
	//是否有应用发版权限
	if !perm_service.HasAppPerm(user, app.Name) {
		return false, "你没有应用的发布权限"
	}
	//环境是否不受发版时间窗口限制
	if config.GetSetting().TimeWindow.Open {
		if !config.GetSetting().TimeWindow.IsSkip(pipe.Namespace) {
			//应用是否在发版时间窗口内
			if len(app.TimeWindow) > 0 && !app.TimeWindow.IsIncluded() {
				return false, fmt.Sprintf("当前时间不允许发版，如需紧急发布请联系应用管理员添加临时授权。应用发版时间窗口为: %s", app.TimeWindow.Desc())
			}
		}
	}

	return true, ""
}

// 测试环境特殊处理
func qaTestEnvValidate(pipe models.Pipeline, user models.User) error {
	if perm_service.IsAdmin(user) {
		return nil
	}
	weekday := time.Now().Weekday()
	if weekday == time.Saturday || weekday == time.Sunday {
		return nil
	}
	hour := time.Now().Hour()
	if hour < 10 || (12 <= hour && hour < 14) || hour >= 19 {
		return nil
	}
	if pipe.Namespace == "fstest" || pipe.Namespace == "fstest-gray" {
		if !perm_service.InOrg(user, "QA") {
			return fmt.Errorf("环境 [fstest，fstest-gray] 在工作时间段 [10:00-12:00, 14:00-19:00] 只允许QA同学发布")
		}
	}
	return nil
}

func BuildAndDeploy(c *gin.Context) {
	var p BuildAndDeployParam
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	jobIds := make([]uint, 0)
	//镜像构建
	buildRet, err := BuildImageWithParam(c, BuildImageParamList{Items: p.BuildImageParam}, false)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range buildRet {
		jobIds = append(jobIds, it.JobId)
	}

	//应用部署
	deployModuleImages := make([]DeployModuleImage, 0)
	for _, it := range buildRet {
		deployModuleImages = append(deployModuleImages, DeployModuleImage{
			GitUrl:    it.GitUrl,
			GitModule: it.GitModule,
			Image:     it.ArtifactImage,
		})
	}
	for idx, _ := range p.DeployParam {
		p.DeployParam[idx].DeployModuleImages = deployModuleImages
	}
	lastBuildJobId := buildRet[len(buildRet)-1].JobId
	deployRet, err := DeployAppWithParam(c, DeployParamList{Items: p.DeployParam}, lastBuildJobId)
	if err != nil {
		//部署任务创建失败时，删除构建任务
		for _, it := range jobIds {
			if job, err := job_service.FindById(it); err == nil {
				_ = job_service.DeleteById(job.ID)
			}
		}
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range deployRet {
		jobIds = append(jobIds, it.JobId)
	}

	//执行任务
	for _, it := range jobIds {
		job, err := job_service.FindById(it)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//todo: 如果任务失败，考虑回收已提交任务
		job_executor.SubmitJob(job)
	}
	web.SuccessJson(c, deployRet)
}

func DeployApp(c *gin.Context) {
	var p DeployParamList
	if err := c.ShouldBindJSON(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	deployRet, err := DeployAppWithParam(c, p, 0)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range deployRet {
		job, err := job_service.FindById(it.JobId)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//todo: 如果任务失败，考虑回收已提交任务
		job_executor.SubmitJob(job)
	}
	web.SuccessJson(c, deployRet)
}

func DeployAppWithParam(c *gin.Context, p DeployParamList, beforeJobId uint) (ret []DeployResult001, er error) {
	ret = make([]DeployResult001, 0)
	user, _ := auth.GetUser(c)

	for _, it := range p.Items {
		pipeline, err := pipeline_service.FindById(it.PipelineId)
		if err != nil {
			return nil, fmt.Errorf("pipeline not found, id: %d, err: %v", it.PipelineId, err.Error())
		}
		it.Remark = strings.ReplaceAll(it.Remark, "\n", `\n`)
		ret = append(ret, DeployResult001{
			DeployParam: it,
			Pipeline:    pipeline,
		})
	}
	for _, it := range ret {
		if err := qaTestEnvValidate(it.Pipeline, user); err != nil {
			return nil, err
		}
	}

	//todo: 需要考虑复制云只能使用复制云父pom，其他环境不能使用复制云父pom的校验
	firstPipe := ret[0].Pipeline
	for _, it := range ret {
		if allow, msg := allowRunDeployJob(it.Pipeline, user); !allow {
			return nil, errors.New(msg)
		}
	}

	for _, it := range ret {
		pipe := it.Pipeline
		if pipe.Status != constant.PIPELINE_STATUS_ENABLED {
			return nil, errors.New("有发布流程处于不可用状态")
		}
		if !pipe.AppModules.Equals(firstPipe.AppModules) {
			return nil, errors.New("发布流程之间的部署模块不一样，不能一起批量发布")
		}
	}

	for _, it := range ret {
		pipe := it.Pipeline
		sp := map[string]string{
			"cluster":   pipe.Cluster,
			"namespace": pipe.Namespace,
			"app":       pipe.App,
		}
		if last, err := job_service.LastByType(models.JOB_TYPE_CD, sp); err == nil {
			if !last.IsEnd() {
				return nil, errors.New(fmt.Sprintf("环境(%s/%s)当前有部署任务正在运行中，请等它执行结束或手动取消后再尝试", pipe.Namespace, pipe.Cluster))
			}
		}
	}

	beforeId := beforeJobId
	for idx, jobItem := range ret {
		pipe := jobItem.Pipeline
		var deployModules datatype.DeployModules
		for _, it := range pipe.AppModules {
			var deployModuleImage *DeployModuleImage
			for _, it2 := range jobItem.DeployModuleImages {
				if it.GitUrl == it2.GitUrl && it.Module == it2.GitModule {
					deployModuleImage = &it2
					break
				}
			}
			if deployModuleImage == nil {
				return nil, fmt.Errorf("没有找到部署模块的镜像，gitUrl: %s, module: %s", it.GitUrl, it.Module)
			}

			pkg := "war"
			if firstPipe.EnvValue("SPRING_BOOT_JAR_APP") == "true" {
				pkg = "jar"
			}
			artifactPathSrc := "/fs-artifact/fs." + pkg
			ctx := strings.ReplaceAll(it.ContextPath, "/", "")
			if ctx == "" {
				ctx = "ROOT"
			}
			artifactPathDst := fmt.Sprintf("/opt/tomcat/webapps/%s.%s", ctx, pkg)

			gitTag := docker.DockerTag2GitTag(docker.GetDockerImageTag(deployModuleImage.Image))
			gitCommitId := "-"
			if artifactRecord, err := artifact_image_service.FindByImage(deployModuleImage.Image); err == nil {
				gitTag = artifactRecord.GitRef
				gitCommitId = artifactRecord.GitCommitId
			}

			deployModules = append(deployModules, datatype.DeployModule{
				GitUrl:            it.GitUrl,
				Module:            it.Module,
				ContextPath:       it.ContextPath,
				Tag:               gitTag,
				CommitID:          gitCommitId,
				ArtifactBaseImage: config.GetConfig().Harbor.ArtifactBaseImage,
				ArtifactImage:     deployModuleImage.Image,
				ArtifactPathSrc:   artifactPathSrc,
				ArtifactPathDst:   artifactPathDst,
			})
		}

		maxSurge := jobItem.MaxSurge
		//为了避免发版时间太长，当pod保留时间很长时，把发布批次设置为100%
		if pipe.PreStopRetainSeconds > 300 {
			maxSurge = "100%"
		}

		jobParams := map[string]interface{}{
			"pipelineId":    pipe.ID,
			"cluster":       pipe.Cluster,
			"namespace":     pipe.Namespace,
			"app":           pipe.App,
			"maxSurge":      maxSurge,
			"remark":        jobItem.Remark,
			"tag":           deployModules.Version(),
			"deployModules": deployModules,
		}
		job := models.Job{
			Status:      models.JOB_PHASE_WAIT,
			Type:        models.JOB_TYPE_CD,
			App:         pipe.App,
			PipelineId:  pipe.ID,
			Author:      user.RealName,
			Params:      jobParams,
			Remark:      jobItem.Remark,
			BeforeJobId: beforeId,
		}

		if err := job_service.Create(&job); err != nil {
			return nil, fmt.Errorf("create job failed, err: %v", err)
		}
		ret[idx].JobId = job.ID
		ret[idx].BeforeJobId = job.BeforeJobId
		taskParams := map[string]interface{}{
			"pipelineId":    pipe.ID,
			"deployModules": deployModules,
			"maxSurge":      maxSurge,
			"remark":        jobItem.Remark,
			"failRollout":   true,
		}
		deployTask := models.Task{
			JobID:          job.ID,
			Status:         models.TASK_PHASE_WAIT,
			Type:           models.TASK_TYPE_DEPLOY,
			Params:         taskParams,
			Attributes:     nil,
			Title:          "服务部署",
			Summary:        "",
			Output:         "",
			Author:         user.RealName,
			TimeoutSeconds: 3600,
			Remark:         jobItem.Remark,
		}

		if err := task_service.Create(&deployTask); err != nil {
			return nil, fmt.Errorf("create task failed, err: %v", err)
		}

		if jobItem.EolinkerTest && config.GetConfig().Eolinker.Enable && !pipe.EolinkerIDs.IsEmpty() {
			eolinkerTask := models.Task{
				JobID:  job.ID,
				Status: models.TASK_PHASE_WAIT,
				Type:   models.TASK_TYPE_EOLINKER,
				Params: map[string]interface{}{
					"app":         pipe.App,
					"cluster":     pipe.Cluster,
					"namespace":   pipe.Namespace,
					"version":     deployModules.Version(),
					"remark":      jobItem.Remark,
					"eolinkerIDs": pipe.EolinkerIDs,
				},
				Attributes:     nil,
				Title:          "接口测试",
				Summary:        "调用Eolinker接口测试",
				Output:         "",
				Author:         user.RealName,
				TimeoutSeconds: 3600,
			}
			if err := task_service.Create(&eolinkerTask); err != nil {
				return nil, fmt.Errorf("create eolinker task failed, err: %v", err)
			}
		}

		if pipe.Webhook.Url != "" {
			webhookTask := models.Task{
				JobID:  job.ID,
				Status: models.TASK_PHASE_WAIT,
				Type:   models.TASK_TYPE_WEBHOOK,
				Params: map[string]interface{}{
					"url":    pipe.Webhook.Url,
					"method": "POST",
					"body": map[string]string{
						"namespace": pipe.Namespace,
						"cluster":   pipe.Cluster,
						"app":       pipe.App,
						"version":   deployModules.Version(),
						"author":    user.RealName,
						"remark":    jobItem.Remark,
					},
				},
				Attributes:     nil,
				Title:          "Webhook",
				Summary:        "调用Webhook接口",
				Output:         "",
				Author:         user.RealName,
				TimeoutSeconds: 120,
			}
			if err := task_service.Create(&webhookTask); err != nil {
				return nil, fmt.Errorf("create webhook task failed, err: %v", err)
			}
		}
		if beforeId == 0 {
			beforeId = job.ID
		}
	}
	return ret, nil
}

func DeployAppWithCurrVersion(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	remark := c.Query("remark")
	if cluster == "" || namespace == "" || app == "" || remark == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster, namespace, app, remark不能为空")
		return
	}
	if cluster == "forceecrm-k8s1" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "复制云暂且不支持该功能")
		return
	}
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "无权限操作")
		return
	}

	pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	deployParam := DeployParam{
		PipelineId:         pipe.ID,
		Remark:             remark,
		MaxSurge:           "50%",
		DeployModuleImages: make([]DeployModuleImage, 0, 10),
	}

	dep, err := k8s_service.DeploymentDetail(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	deployModules, err := k8s_service.GetDeployModules(dep)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(deployModules) != len(pipe.AppModules) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "发布模块数量与k8s中数量不一致")
		return
	}
	for _, mod := range deployModules {
		image := mod.ArtifactImage
		deployParam.DeployModuleImages = append(deployParam.DeployModuleImages, DeployModuleImage{
			GitUrl:    mod.GitUrl,
			GitModule: mod.Module,
			Image:     image,
		})
	}
	p := DeployParamList{
		Items: []DeployParam{deployParam},
	}
	deployRet, err := DeployAppWithParam(c, p, 0)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	for _, it := range deployRet {
		job, err := job_service.FindById(it.JobId)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//todo: 如果任务失败，考虑回收已提交任务
		job_executor.SubmitJob(job)
	}
	web.SuccessJson(c, deployRet)
}

func BuildImageWithCurrVersion(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "无权限操作")
		return
	}
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")
	app := c.Query("app")
	if cluster == "" || namespace == "" || app == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "cluster, namespace, app不能为空")
		return
	}
	remark := c.Query("remark")

	pipe, err := pipeline_service.FirstInEnv(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	p := BuildImageParamList{
		Items: make([]BuildImageParam, 0, 10),
	}

	dep, err := k8s_service.DeploymentDetail(cluster, namespace, app)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	deployModules, err := k8s_service.GetDeployModules(dep)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	if len(deployModules) != len(pipe.AppModules) {
		web.FailJson(c, web.CODE_SERVER_ERROR, "发布模块数量与k8s里的数量不一致")
		return
	}

	for _, mod := range deployModules {
		parentPom := ""
		if parentPom == "" {
			web.FailJson(c, web.CODE_SERVER_ERROR, "请先选择一个父POM")
			return
		}

		mavenImage, err := getMavenImage(pipe.BaseImage)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, "获取Maven镜像失败，"+err.Error())
			return
		}
		p.Items = append(p.Items, BuildImageParam{
			App:              app,
			GitUrl:           mod.GitUrl,
			GitModule:        mod.Module,
			GitTag:           mod.Tag,
			MavenImage:       mavenImage,
			UnitTest:         false,
			ForceCodeCompile: true,
			DependencyCheck:  true,
			ParentPom:        parentPom,
			Remark:           remark,
		})
	}
	buildRet, err := BuildImageWithParam(c, p, false)
	for _, it := range buildRet {
		job, err := job_service.FindById(it.JobId)
		if err != nil {
			web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			return
		}
		//todo: 如果任务失败，考虑回收已提交任务
		job_executor.SubmitJob(job)
	}
}

// 此代码跟api/app下的有重复，后续需要整合优化
func getMavenImage(image string) (string, error) {
	jdk, err := extractJDK(image)
	if err != nil {
		return "", err
	}

	// 根据新的规则选择Maven镜像
	targetJdk := selectMavenJdk(jdk)

	for _, it := range config.GetSetting().MavenImages {
		if strings.HasSuffix(it, targetJdk) {
			return it, nil
		}
	}
	return "", fmt.Errorf("未找到匹配的Maven版本: %s", targetJdk)
}

func selectMavenJdk(jdk string) string {
	// 1. 如果是dragonwell镜像，继续使用dragonwell
	if strings.HasPrefix(jdk, "dragonwell") {
		return jdk
	}

	// 2. 如果是Java 21以上的，继续使用Java 21以上版本
	if strings.HasPrefix(jdk, "openjdk") {
		versionStr := strings.TrimPrefix(jdk, "openjdk")
		if version, err := strconv.Atoi(versionStr); err == nil && version >= 21 {
			return jdk
		}
	}

	// 3. 其他都默认使用Java 21
	return "openjdk21"
}

func extractJDK(image string) (string, error) {
	//查找openjdk
	re := regexp.MustCompile(`openjdk(\d+)`)
	matches := re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "openjdk" + matches[1], nil
	}
	//查找dragonwell jdk
	re = regexp.MustCompile(`dragonwell(\d+)`)
	matches = re.FindStringSubmatch(image)
	if len(matches) >= 2 {
		return "dragonwell" + matches[1], nil
	}
	return "", fmt.Errorf("未找到匹配的JDK版本")
}
