package operation

import (
	"crypto/tls"
	"fmt"
	"fs-k8s-app-manager/config"
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/client/cms"
	"fs-k8s-app-manager/pkg/client/harbor"
	"fs-k8s-app-manager/pkg/client/k8s"
	"fs-k8s-app-manager/pkg/client/kubectl"
	"fs-k8s-app-manager/pkg/middleware/auth"
	"fs-k8s-app-manager/pkg/util/strslice"
	"fs-k8s-app-manager/service/app_service"
	"fs-k8s-app-manager/service/app_version_history_service"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/k8s_service"
	"fs-k8s-app-manager/service/log_service"
	"fs-k8s-app-manager/service/perm_service"
	"fs-k8s-app-manager/service/pipeline_service"
	"fs-k8s-app-manager/service/scale_monitor_log_service"
	"fs-k8s-app-manager/web"
	"fs-k8s-app-manager/web/param"
	"net/url"
	"path"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	log "github.com/sirupsen/logrus"
)

type SearchAppVersionHistoryParam struct {
	param.PageSearch
	Cluster    string `form:"cluster"`
	Namespace  string `form:"namespace" binding:"required"`
	App        string `form:"app"`
	RecordDate string `form:"recordDate" binding:"required"`
}

func TomcatTrafficCount(c *gin.Context) {
	cluster := c.Query("cluster")
	namespace := c.Query("namespace")

	if cluster == "" || namespace == "" {
		web.FailJson(c, web.CODE_CLIENT_ERROR, "some param is empty: cluster,namespace")
		return
	}
	var pipes []models.Pipeline
	var err error
	if namespace == "*" {
		pipes, err = pipeline_service.FindByCluster(cluster)
	} else {
		pipes, err = pipeline_service.FindByClusterAndNamespace(cluster, namespace)
	}
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	go func() {
		data := make([]map[string]interface{}, 0, 200)
		taskSize := 50
		taskCount := len(pipes) / taskSize
		if len(pipes)%taskSize != 0 {
			taskCount += 1
		}
		wg := sync.WaitGroup{}
		wg.Add(taskCount)
		for i := 0; i < taskCount; i++ {
			idx := i
			go func() {
				start := idx * taskSize
				end := (idx + 1) * taskSize
				if end > len(pipes) {
					end = len(pipes)
				}
				ret := tomcatTraffic(pipes[start:end])
				data = append(data, ret...)
				wg.Done()
			}()
		}
		wg.Wait()
		log_service.Create(auth.GetRealName(c), "tomcat-traffic-count", fmt.Sprintf("%s/%s", cluster, namespace), data)
	}()
	web.SuccessJson(c, "操作成功，正在扫描中，结果请稍后在审计日志下查询")

}

func tomcatTraffic(pipes []models.Pipeline) []map[string]interface{} {
	ret := make([]map[string]interface{}, 0, len(pipes))
	accessFile := "tomcat-access." + time.Now().Format("2006-01-02") + ".log"
	//todo: 解决没有找到任何记录时，命令不返回0的问题
	cmd := fmt.Sprintf("cat /opt/tomcat/logs/%s | grep -v k8s-helper | grep -v logback | grep -c -v ^$", accessFile)

PipeLoop:
	for _, pi := range pipes {
		pods, err := k8s_service.ListPod(pi.Cluster, pi.Namespace, pi.App)
		if err != nil || len(pods) == 0 {
			continue
		}
		var counter int64
		podItems := make([]map[string]interface{}, 0, len(pods))
		for _, pod := range pods {
			output, err := kubectl.PodCmd(pi.Cluster, pi.Namespace, pod.Name, cmd, 30*time.Second)
			if err != nil {
				log.Info(err)
				continue PipeLoop
			}
			count, err := strconv.ParseInt(strings.TrimSpace(output), 10, 64)
			if err != nil {
				log.Warn(err)
				continue PipeLoop
			}
			counter += count
			podItem := make(map[string]interface{})
			podItem["name"] = pod.Name
			podItem["tomcatTraffic"] = count
			podItems = append(podItems, podItem)
		}

		item := make(map[string]interface{})
		item["app"] = pi.App
		item["time"] = time.Now().Format("2006/01/02 15:04")
		item["pods"] = podItems
		item["cluster"] = pi.Cluster
		item["namespace"] = pi.Namespace
		item["tomcatTraffic"] = counter
		item["logFile"] = accessFile

		owners := app_service.GetAppMainOwnersWithCache(pi.App)
		ownerNames := make([]string, 0, 5)
		for _, owner := range owners {
			ownerNames = append(ownerNames, "@"+owner)
		}
		item["owner"] = ownerNames
		ret = append(ret, item)
	}
	return ret
}

func ServicePortDump(c *gin.Context) {
	portItems := make([]string, 0, 6000)
	cmsItems := make([]string, 0, 6000)
	cmsItemsV2 := make([]string, 0, 6000)
	cmsItemsV3 := make([]string, 0, 6000)
	for _, clu := range config.GetSetting().Clusters {
		for _, ns := range clu.Namespaces {
			services, err := k8s_service.ListService(clu.Name, ns)
			if err != nil {
				log.Warn("get service fail, ", clu.Name, ns)
				continue
			}
			for _, ser := range services {
				for _, p := range ser.Ports {
					portItems = append(portItems, fmt.Sprintf("%s/%s/%s/%s/%v/%d/%d",
						clu.Name, ns, ser.Name, p.Name, p.TargetPort, p.Port, p.NodePort))
					if p.NodePort > 0 {
						loadBalancer := config.GetLBPools().GetAppLBAddr(clu.Name, ns, ser.Name)
						cmsItems = append(cmsItems, fmt.Sprintf("%s,%s,%s:%d", ns, ser.Name, loadBalancer, p.NodePort))
						cmsItemsV2 = append(cmsItemsV2, fmt.Sprintf("%s,%s,%s,%s:%d", clu.Name, ns, ser.Name, loadBalancer, p.NodePort))
						cmsItemsV3 = append(cmsItemsV3, fmt.Sprintf("%s__%s__%s__%s__%s=%s:%d",
							strings.ReplaceAll(clu.Name, "-", "_"),
							strings.ReplaceAll(ns, "-", "_"),
							strings.ReplaceAll(ser.Name, "-", "_"),
							strings.ReplaceAll(p.Name, "-", "_"),
							strconv.FormatInt(int64(p.Port), 10),
							loadBalancer,
							p.NodePort))
					}
				}
			}
		}
	}
	portItems = append([]string{"time: " + time.Now().Format("2006-01-02 15:04:05")}, portItems...)
	log_service.CreateBySys("端口-备份", "ServicePort", portItems)
	configPaths := strings.Split(config.GetConfig().CMS.ServiceConfigPath, "/")
	configPathsV2 := strings.Split(config.GetConfig().CMS.ServiceConfigPathV2, "/")
	_, err := cms.UpdateConfig(configPaths[0], configPaths[1], strings.Join(cmsItems, "\n"))
	_, err = cms.UpdateConfig(configPathsV2[0], configPathsV2[1], strings.Join(cmsItemsV2, "\n"))
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, nil)
}
func LogClear(c *gin.Context) {
	t := time.Now().AddDate(0, -13, 0)
	beforeCount := log_service.CountAll()
	if err := log_service.DeleteBeforeTime(t); err != nil {
		log.Warn("log clear fail, ", err.Error())
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	}
	afterCount := log_service.CountAll()
	log_service.CreateBySys("日志-清理", "AuditLog", fmt.Sprintf("删除前: %d | 删除后: %d", beforeCount, afterCount))
	web.SuccessJson(c, nil)
}

func MonitorLogClear(c *gin.Context) {
	t := time.Now().AddDate(0, 0, -7)
	beforeCount := scale_monitor_log_service.Count()
	if err := scale_monitor_log_service.DeleteBeforeTime(t); err != nil {
		log.Warn("monitor log clear fail, ", err.Error())
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
	}
	afterCount := scale_monitor_log_service.Count()
	log_service.CreateBySys("日志-清理", "ScaleMonitorLog", fmt.Sprintf("删除前: %d | 删除后: %d", beforeCount, afterCount))
	web.SuccessJson(c, nil)
}

func HarborAppImageGC(c *gin.Context) {
	dryRun := c.DefaultQuery("dryRun", "true") != "false"
	log.Info("start harbor gc")
	k8sImages := make(map[string]bool)
	data := make(map[string]interface{})
	data["dryRun"] = dryRun
	conf := config.GetConfig().Harbor
	appProjectPrefix := fmt.Sprintf("%s/%s", conf.Host, conf.AppProject)
	for _, clu := range config.GetSetting().Clusters {
		for _, ns := range clu.Namespaces {
			replicaSets, err := k8s_service.ListReplicaSet(clu.Name, ns)
			if err != nil {
				log.Warn("get replicaset fail, ", clu.Name, ns)
				web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
			}
			for _, rs := range replicaSets {
				img := rs.Container0Image
				if !strings.HasPrefix(img, appProjectPrefix) {
					continue
				}
				k8sImages[img] = true
			}
		}
	}

	keys := make([]string, 0, len(k8sImages))
	for k, _ := range k8sImages {
		keys = append(keys, k)
	}
	data["k8sImages"] = keys

	hc := harbor.Create()

	repositories, err := hc.GetAllRepositories(conf.AppProject)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	success := make([]string, 0, 200)
	fail := make([]string, 0, 200)

	for _, repo := range repositories {
		artifacts, err := hc.GetAllArtifacts(conf.AppProject, repo.Name)
		if err != nil {
			log.Warnf("can't get artifacts, proj: %s, repo: %s, err: %s", conf.AppProject, repo.Name, err.Error())
			continue
		}
		for _, arti := range artifacts {
			for _, tag := range arti.Tags {
				image := fmt.Sprintf("%s/%s/%s:%s", conf.Host, conf.AppProject, repo.Name, tag.Name)
				if _, found := k8sImages[image]; !found {
					var err error
					if !dryRun {
						err = harbor.Create().DeleteTag(conf.AppProject, repo.Name, arti.Digest, tag.Name)
					}
					if err != nil {
						log.Warn("harbor image delete fail, image: ", image, " ,err: ", err.Error())
						fail = append(fail, fmt.Sprintf("%s(%s)", image, err.Error()))
					} else {
						log.Info("harbor image delete success, image: " + image)
						success = append(success, image)
					}
				}
			}
		}
	}
	data["harborDelSuccess"] = success
	data["harborDelFail"] = fail
	log_service.CreateBySys("Harbor-GC", conf.Host, data)
	web.SuccessJson(c, data)
}

// HarborGC 镜像清理，受Harbor api限制，产生的rpc调用可能会比较多，耗时比较长
// 备注： 这里只对Tag做删除，不删除Artifact。所以在Harbor上做GC时请选择选项【允许回收无 tag 的 artifacts】
func HarborGC(c *gin.Context) {
	dryRun := c.DefaultQuery("dryRun", "true") != "false"
	log.Info("start harbor gc")
	k8sImages := make(map[string]bool)
	data := make(map[string]interface{})
	data["dryRun"] = dryRun
	conf := config.GetConfig().Harbor
	for _, clu := range config.GetSetting().Clusters {
		for _, ns := range clu.Namespaces {
			items, err := k8s.GetReplicaSetList(clu.Name, ns)
			if err != nil {
				web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("can't get rs from k8s, %s/%s, err: %s", clu.Name, ns, err.Error()))
				return
			}
			for _, item := range items.Items {
				initContainers := item.Spec.Template.Spec.InitContainers
				if len(initContainers) > 0 {
					for _, c := range initContainers {
						img := c.Image
						//把代理镜像转换为原始镜像
						img = clu.ProxyImage2NormalImage(img)
						k8sImages[img] = true
					}
				}
			}
		}
	}

	keys := make([]string, 0, len(k8sImages))
	for k, _ := range k8sImages {
		keys = append(keys, k)
	}
	data["k8sImages"] = keys

	hc := harbor.Create()
	imageProject := conf.ArtifactProject

	repositories, err := hc.GetAllRepositories(imageProject)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}

	success := make([]string, 0, 200)
	fail := make([]string, 0, 200)

	for _, repo := range repositories {
		artifacts, err := hc.GetAllArtifacts(imageProject, repo.Name)
		if err != nil {
			log.Warnf("can't get artifacts, proj: %s, repo: %s, err: %s", conf.ArtifactProject, repo.Name, err.Error())
			continue
		}
		for _, arti := range artifacts {
			for _, tag := range arti.Tags {
				image := fmt.Sprintf("%s/%s/%s:%s", conf.Host, imageProject, repo.Name, tag.Name)
				if _, found := k8sImages[image]; !found {
					var err error
					if !dryRun {
						err = harbor.Create().DeleteTag(imageProject, repo.Name, arti.Digest, tag.Name)
					}
					if err != nil {
						log.Warn("harbor image delete fail, image: ", image, " ,err: ", err.Error())
						fail = append(fail, fmt.Sprintf("%s(%s)", image, err.Error()))
					} else {
						log.Info("harbor image delete success, image: " + image)
						success = append(success, image)
					}
				}
			}
		}
	}
	data["harborDelSuccess"] = success
	data["harborDelFail"] = fail
	log_service.CreateBySys("Harbor-GC", conf.Host, data)
	web.SuccessJson(c, data)
}

type nginxResponse struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Mtime string `json:"mtime"`
}

func WarGC(c *gin.Context) {
	dryRun := c.DefaultQuery("dryRun", "true") != "false"
	k8sWars, err := ScanAllWar()
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	warHost, err := url.Parse(config.GetConfig().War.Host)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, err.Error())
		return
	}
	warHost.Path = path.Join(warHost.Path, "git.firstshare.cn")
	nginxWars, err := getNginxWars(*warHost)
	if err != nil {
		web.FailJson(c, web.CODE_SERVER_ERROR, fmt.Sprintf("failed to get nginxWars: %v", err))
		return
	}
	output := make(map[string]interface{})
	output["dryRun"] = dryRun
	output["k8sWars"] = k8sWars
	output["nginxWars"] = nginxWars
	sucItems := make([]string, 0, 200)
	failItems := make([]string, 0, 200)
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	for _, nw := range nginxWars {
		if !strslice.Find(k8sWars, nw) {
			if !dryRun {
				time.Sleep(20 * time.Millisecond)
				rep, err := client.R().SetBasicAuth(config.GetConfig().War.Username, config.GetConfig().War.Password).Get(nw + ".delete")
				if err != nil || rep.StatusCode() != 200 {
					failItems = append(failItems, nw)
					log.Warn("war delete fail, url: ", nw, " ,err: ", err.Error())
					continue
				}
			}
			sucItems = append(sucItems, nw)
			log.Info("war delete success, url: ", nw)
		}
	}
	output["deleteSuccess"] = sucItems
	output["deleteFail"] = failItems
	log_service.CreateBySys(fmt.Sprintf("war-gc"), "", output)
	web.SuccessJson(c, output)
	return
}

func getNginxWars(url url.URL) ([]string, error) {
	var (
		nginxResp []nginxResponse
		wars      []string
	)
	// clone
	client := resty.New().SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	repo, err := client.R().SetBasicAuth(config.GetConfig().War.Username, config.GetConfig().War.Password).SetResult(&nginxResp).Get(url.String())
	if err != nil || repo.StatusCode() != 200 {
		return wars, err
	}
	for _, v := range nginxResp {
		u := url
		if v.Type == "directory" {
			u.Path = path.Join(u.Path, v.Name)
			w, err := getNginxWars(u)
			if err != nil {
				break
			}
			wars = append(wars, w...)
		} else {
			u.Path = path.Join(u.Path, v.Name)
			wars = append(wars, u.String())
		}
	}
	time.Sleep(10 * time.Millisecond)
	sort.Strings(wars)
	return wars, err
}

type War struct {
	Cluster      string
	Namespace    string
	App          string
	Path         string
	LastModified string
	Remark       string
}

func getWarPath(m datatype.DeployModule) string {
	u, _ := url.Parse(config.GetConfig().War.Host)
	replacer := strings.NewReplacer("http://", "", "https://", "", ".git", "")
	u.Path = path.Join(u.Path, replacer.Replace(m.GitUrl))

	if m.Module == "" {
		u.Path = path.Join(u.Path, "__blank__")
	} else {
		u.Path = path.Join(u.Path, m.Module)
	}
	u.Path = path.Join(u.Path, m.Tag+".war")
	return u.String()
}

func ScanAllWar() ([]string, error) {
	var wars = make([]string, 0, 200)
	for _, clu := range config.GetSetting().Clusters {
		if !clu.Enable {
			continue
		}
		for _, ns := range clu.Namespaces {
			// 有错误则终止，防止获取的war包不全，导致正在使用的war包被删除了
			replicaSets, err := k8s.GetReplicaSetList(clu.Name, ns)
			if err != nil {
				return nil, fmt.Errorf("can't get replicas from %s/%s: %v", clu.Name, clu.Namespaces, err)
			}
			for _, rs := range replicaSets.Items {
				if v := k8s_service.CetEnvValue(rs.Spec.Template.Spec.Containers[0].Env, "FS_DEPLOY_MODULES"); v != "" {
					modules := datatype.DeployModules{}
					if err := modules.FromJson(v); err == nil {
						for _, m := range modules {
							warPath := getWarPath(m)
							if !strslice.Find(wars, warPath) {
								wars = append(wars, warPath)
							}
						}
					}
				}
			}
			time.Sleep(20 * time.Millisecond)
		}
	}
	sort.Strings(wars)
	return wars, nil
}

func SearchAppVersionHistory(c *gin.Context) {
	var p SearchAppVersionHistoryParam
	if err := c.ShouldBindQuery(&p); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}

	data := make(map[string]interface{})

	if items, err := app_version_history_service.Search(p.Cluster, p.Namespace, p.App, p.RecordDate, p.Page, p.Limit); err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	} else {
		data["data"] = items
		data["count"] = app_version_history_service.Count(p.Cluster, p.Namespace, p.App, p.RecordDate)
	}
	web.SuccessJson(c, data)
}

func DumpAppVersionHistory(c *gin.Context) {
	msgs := make([]string, 0, 2000)
	for _, cluster := range config.GetSetting().Clusters {
		if !cluster.IsFxiaokeCloud() {
			continue
		}
		for _, namespace := range cluster.Namespaces {
			ret, err := k8s.GetDeploymentList(cluster.Name, namespace)
			if err != nil {
				msgs = append(msgs, fmt.Sprintf("%s/%s, error: %s", cluster.Name, namespace, err.Error()))
				log.Error()
				continue
			}
			for _, dep := range ret.Items {
				if *dep.Spec.Replicas < 1 {
					msgs = append(msgs, fmt.Sprintf("%s/%s/%s, skipped, replicas < 1", cluster.Name, namespace, dep.Name))
					continue
				}
				if !pipeline_service.ExistInEnv(cluster.Name, namespace, dep.Name) {
					msgs = append(msgs, fmt.Sprintf("%s/%s/%s, skipped, pipeline not exist", cluster.Name, namespace, dep.Name))
					continue
				}
				modules, err := k8s_service.GetDeployModules(dep)
				if err != nil {
					msgs = append(msgs, fmt.Sprintf("%s/%s/%s, error: %s", cluster.Name, namespace, dep.Name, err.Error()))
					continue
				}
				ver, _, _ := k8s_service.GetDeployTag(dep)
				err = app_version_history_service.Save(&models.AppVersionHistory{
					App:           dep.Name,
					Cluster:       cluster.Name,
					Namespace:     namespace,
					Version:       ver,
					RecordDate:    time.Now().Format("2006-01-02"),
					DeployModules: modules,
					DeployRemark:  dep.Annotations["fxiaoke.com/deploy-remark"],
					DeployAuthor:  dep.Annotations["fxiaoke.com/deploy-user"],
					DeployTime:    dep.Annotations["fxiaoke.com/deploy-time"],
				})
				if err != nil {
					msgs = append(msgs, fmt.Sprintf("%s/%s/%s, error: %s", cluster.Name, namespace, dep.Name, err.Error()))
					continue
				}
				msgs = append(msgs, fmt.Sprintf("%s/%s/%s, success", cluster.Name, namespace, dep.Name))
			}
		}
	}
	log_service.CreateBySys("app-version-history-dump", "", msgs)
	web.SuccessJson(c, msgs)
}

func CmdbOwner(c *gin.Context) {
	items, err := cmdb_service.GetDataFromCms()
	if err != nil {
		web.FailJson(c, web.CODE_CLIENT_ERROR, err.Error())
		return
	}
	web.SuccessJson(c, items)
}

func CmdbSync(c *gin.Context) {
	user, _ := auth.GetUser(c)
	if !perm_service.IsAdmin(user) {
		panic("只有管理员才有权限操作")
	}
	cmdb_service.SyncCmdbFromCRM()
	web.SuccessJson(c, nil)
}
