<template>
  <div class="app-container">
    <div>
      <el-form :inline="true" :model="searchForm" class="demo-form-inline" @keyup.enter.native="loadTableData"
        @submit.native.prevent>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" filterable size="small" style="width: 100px">
            <el-option label="所有" value=""></el-option>
            <el-option v-for="item in this.statusOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="集群">
          <el-select v-model="searchForm.cluster" filterable size="small">
            <el-option label="所有" value=""></el-option>
            <el-option v-for="item in this.clusterOptions" :key="item.name" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="环境">
          <el-select v-model="searchForm.namespace" filterable size="small">
            <el-option label="所有" value=""></el-option>
            <el-option v-for="item in this.namespaceOptions" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="应用">
          <el-input v-model.trim="searchForm.app" placeholder="支持模糊匹配" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item label="镜像">
          <el-input v-model.trim="searchForm.image" placeholder="支持模糊匹配" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item label="JVM参数">
          <el-input v-model.trim="searchForm.javaOpts" placeholder="支持模糊匹配" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="loadTableData" size="small">查询</el-button>

          <export-button :table-ref="this.$refs.table001"></export-button>
        </el-form-item>
      </el-form>
    </div>

    <el-pagination :current-page="searchForm.page" :page-sizes="[20, 50, 100, 200, 400, 1000, 2000, 5000]"
      :page-size="searchForm.limit" layout="total,prev,pager,next,sizes" :total="tableData.count"
      style="display: inline-block" @size-change="pageSizeChange" @current-change="pageChange">
    </el-pagination>
    <el-button type="text" size="small" @click="loadRunningPod"
      style="display: inline-block;color:#999;">加载运行副本</el-button>
    <div style="display: inline-block; margin-left: 50px; font-size: 12px; color: orangered">
      资源统计： {{ this.resourceTotal }}
    </div>
    <el-table ref="table001" v-loading="tableLoading" :data="tableData.data" element-loading-text="Loading" border fit
      highlight-current-row>
      <el-table-column type="index">
      </el-table-column>
      <el-table-column label="应用名" sortable prop="app">
      </el-table-column>
      <el-table-column label="状态" prop="status" width="80">
      </el-table-column>
      <el-table-column label="等级" sortable prop="appLevel" width="80px">
      </el-table-column>
      <el-table-column label="集群" sortable prop="cluster" width="100px">
      </el-table-column>
      <el-table-column label="运行环境" sortable prop="namespace">
      </el-table-column>
      <el-table-column label="镜像" sortable prop="baseImage">
        <template slot-scope="scope">
          <span>{{ scope.row.baseImage.substring(scope.row.baseImage.lastIndexOf("/") + 1) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源池" sortable prop="schedule.node">
      </el-table-column>
      <el-table-column label="开关">
        <template slot-scope="scope">
          <div v-for="(item, key) in scope.row.options" :key="key">
            <el-tag style="padding: 1px;margin:1px 0; line-height: normal;height:unset;font-size: 10px;" v-if="item">{{
              key }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="副本" sortable width="80px" prop="replicas" align="center">
      </el-table-column>
      <el-table-column label="运行副本" width="110px" v-if="showRunningPod" sortable align="center" prop="extraAttr.runningPodNum">
        <template slot-scope="scope">
          <span>{{ scope.row.extraAttr.runningPodNum }}</span>
        </template>
      </el-table-column>
      <el-table-column label="JVM参数" width="200px" prop="jvmOpts" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="CPU" width="90px" sortable prop="resources.limitCPU">
        <template slot-scope="scope" v-if="scope.row.resources">
          {{ scope.row.resources.requestCPU.toFixed(1) }} - {{ scope.row.resources.limitCPU.toFixed(1) }}
        </template>
      </el-table-column>
      <el-table-column label="内存 (MB)" width="120px" sortable prop="resources.limitMemory">
        <template slot-scope="scope" v-if="scope.row.resources">
          {{ scope.row.resources.requestMemory }} - {{ scope.row.resources.limitMemory }}
        </template>
      </el-table-column>
      <el-table-column label="负责人" sortable prop="appOwners">
      </el-table-column>
      <el-table-column label="" width="120px">
        <template slot-scope="scope">
          <router-link :to="{ name: 'app-pipeline-edit', query: { pipelineId: scope.row.id } }" target="_blank" style="">
            <span style="color:#409EFF;font-weight: 500;">编辑</span>
          </router-link>
          <router-link :to="{ name: 'cicd-app-deploy', query: { app: scope.row.app } }" target="_blank"
            style="margin-left: 2px;">
            <span style="color:#409EFF;font-weight: 500;">发布流程</span>
          </router-link>
          <br />
          <el-button type="text" @click="removePipe(scope.row)" style="color: #fc8133">下线</el-button>
          <router-link
            :to="{ name: 'pod-index', query: { cluster: scope.row.cluster, namespace: scope.row.namespace, app: scope.row.app } }"
            target="_blank" style="margin-left: 2px;">
            <span style="color:#409EFF;font-weight: 500;">实例管理</span>
          </router-link>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>

import { removePipeline, searchPipeline } from "@/api/pipeline";
import ExportButton from "@/views/components/export-button.vue";
import { searchApp } from "@/api/app";
import { deploymentDetail, searchDeployment } from "@/api/k8s/app";
import fa from "element-ui/src/locale/lang/fa";

export default {
  name: "pipelineAnalysis",
  data() {
    return {
      appOwners: {},
      appLevels: {},
      tableData: {
        count: 0,
        data: []
      },
      statusOptions: [
        { label: "可用", value: "enabled" },
        { label: "禁用", value: "disabled" },
        { label: "已迁移", value: "migrated" },
      ],
      tableLoading: false,
      showRunningPod: false,
      searchForm: {
        cluster: "",
        namespace: "",
        app: "",
        image: "",
        status: "",
        javaOpts: "",
        options: "",
        page: 1,
        limit: 20,
      }
    }
  },
  components: { ExportButton },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
    namespaceOptions: function () {
      let ret = []
      for (let clu of this.$settings.clusters) {
        for (let ns of clu.namespaces) {
          if (!ret.includes(ns)) {
            ret.push(ns)
          }
        }
      }
      return ret
    },
    resourceTotal: function () {
      if (!this.tableData || !this.tableData.data || this.tableData.data.length === 0) {
        return "--"
      }
      let requestCPUTotal = 0;
      let limitCPUTotal = 0;
      let requestMemoryTotal = 0;
      let limitMemoryTotal = 0;
      for (let item of this.tableData.data) {
        if (item.resources) {
          requestCPUTotal += item.resources.requestCPU * item.replicas;
          limitCPUTotal += item.resources.limitCPU * item.replicas;
          requestMemoryTotal += item.resources.requestMemory * item.replicas;
          limitMemoryTotal += item.resources.limitMemory * item.replicas;
        }
      }
      return `CPU: ${(requestCPUTotal.toFixed(1) + " - " + limitCPUTotal.toFixed(1))} | 内存: ${(requestMemoryTotal + " - " + limitMemoryTotal)}`
    }
  },
  mounted() {
    let namespace = this.$route.query.namespace
    if (namespace) {
      this.searchForm.namespace = namespace
    }
    let keyword = this.$route.query.keyword
    if (keyword) {
      this.searchForm.keyword = keyword
    }
    this.loadApps(this.loadTableData);
  },
  methods: {
    loadTableData() {
      if (this.searchForm.limit >= 200) {
        this.$message.warning("查询数据比较多时，页面渲染时间比较慢，请耐心等候...");
      }
      this.tableLoading = true;
      this.showRunningPod = false;
      searchPipeline(this.searchForm).then(response => {
        let tableData = response.data.data;
        for (let it of tableData) {
          it.appOwners = this.appOwners[it.app] || "-"
          it.appLevel = this.appLevels[it.app] || "-"
        }
        this.tableData = response.data;
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    loadRunningPod() {
      if (!this.searchForm.cluster) {
        this.$message.warning("如果要加载运行副本，请先选择一个集群");
        return;
      }
      this.tableLoading = true;
      this.showRunningPod = true;
      searchDeployment(this.searchForm.cluster, "*").then(response => {
        for (let it of response.data) {
          for (let pipe of this.tableData.data) {
            if (pipe.cluster === it.cluster && pipe.namespace === it.namespace && pipe.app === it.name) {
              pipe.extraAttr.runningPodNum = it.replicas
              break
            }
          }
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
      })
    },
    pageChange(page) {
      this.searchForm.page = page;
      this.loadTableData();
    },
    pageSizeChange(v) {
      this.searchForm.limit = v;
      this.loadTableData();
    },
    pipelinePage(row) {
      this.$router.push({ name: 'cicd-app-deploy', query: { "app": row.app } });
    },
    loadApps(callback) {
      this.tableLoading = true;
      searchApp({ keyword: "", page: 1, limit: 2000 }).then(response => {
        for (let it of response.data.data) {
          this.appOwners[it.name] = it.mainOwner
          this.appLevels[it.name] = it.level
        }
      }).catch((e) => {
        this.$message.error(e.message);
      }).finally(() => {
        this.tableLoading = false;
        if (callback) {
          callback()
        }
      })
    },
    removePipe(row) {
      this.$prompt('请输入应用名', '下线确认提示', {
        confirmButtonText: '继续下线',
        confirmButtonClass: 'el-button--danger',
        cancelButtonText: '取消',
        inputValue: row.app,
      }).then(({ value }) => {
        if (value !== row.app) {
          this.$message.info("应用名输入错误")
          return
        }
        removePipeline(row.id).then(response => {
          this.$message.success("操作成功")
          this.tableData.data = this.tableData.data.filter(item => item.id !== row.id);
          this.tableData.count = this.tableData.data.length;
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
        })
      }).catch(() => {
        console.log("取消下线确认操作")
      });
    },
  }
}
</script>
