<template>
  <div>
    <div style="font-size: 12px;margin-bottom: 3px;color: #777;">
      批量清理命令：all-batch-kubectl-cloud.sh "kubectl delete deployment -n image-preheat -l app=fs-image-preheat-job"
    </div>
    <el-row>
      <el-col :span="12" v-for="clu in clusterOptions" :key="clu.name">
        <image-preheat-job :cluster="clu.name"></image-preheat-job>
      </el-col>
    </el-row>
  </div>
</template>
<script>

import ImagePreheatJob from "@/views/cluster/image-preheat-job.vue";

export default {
  name: "image-preheat-job-list",
  data() {
    return {
    }
  },
  components: {ImagePreheatJob},
  mounted() {
  },
  computed: {
    clusterOptions: function () {
      return this.$settings.clusters;
    },
  },
  methods: {}
}
</script>

