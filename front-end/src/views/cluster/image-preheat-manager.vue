<template>
  <div v-loading="loading">
    <el-button icon="el-icon-circle-plus-outline" type="text" @click="image_preheat_dialog()">收集预热镜像</el-button>
    <el-card class="box-card">
      <el-table
        ref="table001"
        :data="this.tableData"
        row-key="name"
        size="small"
        stripe
      >
        <el-table-column
          type="index"
          width="50">
        </el-table-column>
        <el-table-column width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="image_preheat_detail(scope.row)" style="padding: 5px 10px;">详情</el-button>
            <el-button size="mini" type="primary" @click="image_preheat_execute_dialog(scope.row)" style="padding: 5px 10px;">预热</el-button>
            <el-button size="mini" type="danger" @click="image_preheat_delete(scope.row)" style="padding: 5px 10px;">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name">
        </el-table-column>
        <el-table-column label="收集时间" prop="CreatedAt">
          <template slot-scope="scope">
            {{ scope.row.CreatedAt.split(".")[0] }}
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="remark">
        </el-table-column>
        <el-table-column label="创建人" prop="creator">
        </el-table-column>
        <el-table-column label="收集条件" prop="query" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ JSON.stringify(scope.row.query, null, 2) }}
          </template>
        </el-table-column>
        <el-table-column label="镜像个数" prop="images" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.images.length }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog title="收集预热镜像" :visible.sync="dialogVisible" v-loading="imagePreheatLoading">
      <el-form label-width="120px" :model="form" ref="form001">
        <el-form-item label="名称" style="margin-bottom: 5px;">
          <el-input v-model="form.name" placeholder="请输入名称"></el-input>
        </el-form-item>
        <el-form-item label="描述" style="margin-bottom: 5px;">
          <el-input v-model="form.remark" placeholder="请输入描述"></el-input>
        </el-form-item>
        <el-form-item label="最近构建的镜像" style="margin-bottom: 5px;">
          <div style="color:#888;">
            对最近
            <el-input-number v-model="form.imageRecentHours" controls-position="right" :min="0" :max="480" :step="12" style="width: 120px; "></el-input-number>
            小时内构建的应用镜像进行预热
          </div>
        </el-form-item>
        <el-form-item label="镜像所属环境" style="margin-bottom: 5px;">
          <el-select v-model="form.imageInEnvs" multiple filterable clearable placeholder="请选择环境" style="width: 100%;">
            <el-option
              v-for="env in evnOptions"
              :key="env"
              :label="env"
              :value="env">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指定的镜像" style="margin-bottom: 5px;">
          <el-select v-model="form.specifiedImages" multiple filterable allow-create clearable placeholder="请选择镜像" style="width: 100%;">
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="create_image_preheat()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="查看详情" :visible.sync="detailDialogVisible" width="50%" top="5vh">
      <div style="margin-top: -50px;overflow: auto;">
        <div style="text-align: center">
          <clipboard-icon :text="this.detail" button-text="一键复制内容"></clipboard-icon>
        </div>
        <pre style="white-space: pre-wrap;border: solid 1px #eee;padding:5px;margin-top: 0;max-height: 600px;overflow-y: auto;">{{ detail }}</pre>
      </div>
    </el-dialog>

    <el-dialog title="执行镜像预热" :visible.sync="executeDialogVisible" width="80%" top="5vh">
      <el-form label-width="120px" :model="executeForm" ref="executeForm001" style="margin-top: -20px" v-loading="loading2">
        <el-form-item label="预热名称" prop="imagePreheatName" style="margin-bottom:0">
          {{executeForm.imagePreheatName}}
        </el-form-item>
        <el-form-item label="预热创建时间" prop="imagePreheatCreatedAt" style="margin-bottom:0">
          {{executeForm.imagePreheatCreatedAt}}
        </el-form-item>
        <el-form-item label="预热任务个数" prop="jobNumber" style="margin-bottom:0">
          <el-input-number v-model="executeForm.jobNumber" controls-position="right" :min="1" :max="100" :step="1"></el-input-number>
        </el-form-item>
        <el-form-item label="请选择集群" prop="cluster" style="margin-bottom:0">
          <el-button size="mini"  @click="clusterWithImageProxy">配置了镜像代理库的所有集群</el-button>
          <el-button size="mini" @click="clusterWithNeedImagePreheat">大版本需要镜像预热的所有集群</el-button>
          <el-button size="mini" @click="clusterAll">全选</el-button>
          <el-button size="mini" @click="clusterNone">清空</el-button>
          <el-checkbox-group v-model="executeForm.clusters">
            <div style="padding: 10px;border: solid 1px #eee;background-color: #eee;">
              <el-row>
                <el-col :span="8" v-for="cluster in clusterOptions" :key="cluster.name" style="line-height: 14px;">
                  <el-checkbox :label="cluster.name">{{ cluster.name }} | {{ cluster.description }}</el-checkbox>
                </el-col>
              </el-row>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item>
          <div style="text-align: left;padding-top: 20px;">
            <el-button type="primary" @click="image_preheat_execute">执行预热</el-button>
            <el-button @click="executeDialogVisible = false">取消</el-button>
            <el-button type="text" @click="downloadJobYaml">下载预热YAML文件</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>

import {createImagePreheat, deleteImagePreheat, downloadJobYaml, executeImagePreheat, searchImagePreheat} from "@/api/image_preheat";
import ClipboardIcon from "@/views/components/clipboard-icon.vue";

export default {
  name: "image-preheat-manager",
  data() {
    return {
      loading: false,
      loading2: false,
      imagePreheatLoading: false,
      tableData: [],
      evnOptions: [],
      dialogVisible: false,
      detailDialogVisible: false,
      executeDialogVisible: false,
      executeForm: {
        imagePreheatId: "",
        imagePreheatName: "",
        imagePreheatCreatedAt: "",
        jobNumber: 6,
        clusters: []
      },
      detail: {},
      form: {
        name: "",
        remark: "",
        imageRecentHours: 24,
        imageInEnvs: [
          "k8s0/foneshare-gray",
          "k8s0/foneshare-stage",
          "k8s1/foneshare-gray",
          "k8s1/foneshare-stage",
        ],
        specifiedImages: [
          "reg.foneshare.cn/base/fs-tomcat8:openjdk8",
          "reg.foneshare.cn/base/fs-tomcat8:openjdk11",
          "reg.foneshare.cn/base/fs-tomcat8:openjdk17",
          "reg.foneshare.cn/base/fs-tomcat8:openjdk21",
          "reg.foneshare.cn/base/fs-tomcat8:openjdk24",
          "reg.foneshare.cn/base/fs-tomcat9:openjdk8",
          "reg.foneshare.cn/base/fs-tomcat9:openjdk11",
          "reg.foneshare.cn/base/fs-tomcat9:openjdk17",
          "reg.foneshare.cn/base/fs-tomcat9:openjdk21",
          "reg.foneshare.cn/base/fs-tomcat9:openjdk24",
          "reg.foneshare.cn/base/fs-tomcat8:ali-dragonwell8"
        ],

      }

    }
  },
  components: {ClipboardIcon},
  mounted() {
    for (let clu of this.$settings.clusters) {
      for (let ns of clu.namespaces) {
        this.evnOptions.push(`${clu.name}/${ns}`)
      }
    }
    this.loadData()
  },
  computed: {
    clusterOptions() {
      return this.$settings.clusters;
    }
  },
  methods: {
    loadData() {
      this.loading = true;
      searchImagePreheat({"keywords": ""}).then(response => {
        this.tableData = response.data;
      }).catch((e) => {
        console.error(e.message)
      }).finally(() => {
        this.loading = false;
      })
    },
    image_preheat_dialog() {
      this.dialogVisible = true;
    },
    create_image_preheat() {
      if (this.form.name === "") {
        this.$message.error("请输入名称");
        return;
      }
      this.$confirm('确定执行操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.imagePreheatLoading = true;
        createImagePreheat(this.form).then(response => {
          this.$message.success("创建成功");
          this.dialogVisible = false;
          this.loadData();
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.imagePreheatLoading = false;
        })
      }).catch(() => {
      });
    },
    image_preheat_detail(row) {
      this.detail = JSON.stringify(row, null, 2);
      this.detailDialogVisible = true;
    },
    image_preheat_delete(row) {
      this.$confirm('确定删除预热Job吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteImagePreheat(row.ID).then(response => {
          this.$message.success("删除成功");
          this.loadData();
        }).catch((e) => {
          this.$message.error(e.message);
        })
      }).catch(() => {
      });
    },
    image_preheat_execute_dialog(row) {
      this.executeForm.imagePreheatId = row.ID;
      this.executeForm.imagePreheatName = row.name;
      this.executeForm.imagePreheatCreatedAt = row.CreatedAt;
      this.executeDialogVisible = true;
    },
    image_preheat_execute() {
      if (this.executeForm.clusters.length < 1) {
        this.$message.error("请选择需要预热的集群");
        return;
      }
      this.$confirm('确定预热镜像吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading2 = true;
        executeImagePreheat(this.executeForm).then(response => {
          this.$message.success(response.data);
          this.loadData();
        }).catch((e) => {
          this.$message.error(e.message);
        }).finally(() => {
          this.loading2 = false;
        })
      }).catch(() => {
      });
    },
    clusterNone() {
      this.executeForm.clusters = [];
    },
    clusterAll() {
      this.executeForm.clusters = this.$settings.clusters.map(cluster => cluster.name);
    },
    clusterWithImageProxy() {
      let ret = []
      for (let clu of this.$settings.clusters) {
        if (clu.imageRegistryProxy) {
          ret.push(clu.name);
        }
      }
      this.executeForm.clusters = ret;
    },
    clusterWithNeedImagePreheat() {
      let ret = []
      for (let clu of this.$settings.clusters) {
        if (clu.needImagePreheat) {
          ret.push(clu.name);
        }
      }
      this.executeForm.clusters = ret;
    },
    downloadJobYaml() {
      downloadJobYaml({
        imagePreheatId: this.executeForm.imagePreheatId,
        jobNumber: this.executeForm.jobNumber,
        clusters: this.executeForm.clusters,
        operate: this.executeForm.operate
      })
    }
  }
}
</script>

<style>
</style>
